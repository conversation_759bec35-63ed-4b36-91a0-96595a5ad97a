<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>登录功能测试页面</title>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            margin: 0;
            padding: 20px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
        }
        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            border-radius: 10px;
            padding: 30px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.3);
        }
        h1 {
            color: #667eea;
            text-align: center;
            margin-bottom: 30px;
        }
        .test-section {
            margin: 30px 0;
            padding: 20px;
            background: #f8f9fa;
            border-radius: 8px;
            border-left: 4px solid #667eea;
        }
        .test-button {
            background: #667eea;
            color: white;
            border: none;
            padding: 12px 24px;
            border-radius: 5px;
            cursor: pointer;
            font-size: 16px;
            margin: 10px;
        }
        .test-button:hover {
            background: #5a6fd8;
        }
        .test-button.success {
            background: #28a745;
        }
        .test-button.error {
            background: #dc3545;
        }
        .test-results {
            margin-top: 20px;
            padding: 15px;
            background: white;
            border-radius: 5px;
            border: 1px solid #ddd;
        }
        .status-indicator {
            display: inline-block;
            width: 12px;
            height: 12px;
            border-radius: 50%;
            margin-right: 8px;
        }
        .status-success { background: #28a745; }
        .status-error { background: #dc3545; }
        .status-warning { background: #ffc107; }
        .status-info { background: #17a2b8; }
        .test-log {
            background: #f8f9fa;
            border: 1px solid #ddd;
            border-radius: 4px;
            padding: 10px;
            margin-top: 10px;
            font-family: monospace;
            font-size: 12px;
            max-height: 200px;
            overflow-y: auto;
        }
        .account-selector {
            margin: 10px 0;
        }
        .account-selector select {
            padding: 8px 12px;
            border: 1px solid #ddd;
            border-radius: 4px;
            font-size: 14px;
        }
        .loading {
            display: inline-block;
            width: 16px;
            height: 16px;
            border: 2px solid #f3f3f3;
            border-top: 2px solid #667eea;
            border-radius: 50%;
            animation: spin 1s linear infinite;
        }
        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🔐 登录功能测试工具</h1>
        
        <div class="test-section">
            <h2>测试账号配置</h2>
            <div class="account-selector">
                <label for="testAccount">选择测试账号:</label>
                <select id="testAccount">
                    <option value="<EMAIL>|Gomyhire@123456"><EMAIL></option>
                    <option value="<EMAIL>|Sky@114788"><EMAIL></option>
                    <option value="<EMAIL>|wrongpassword">无效账号 (测试错误处理)</option>
                </select>
            </div>
        </div>
        
        <div class="test-section">
            <h2>基础功能测试</h2>
            <button class="test-button" onclick="testLoginButtonBinding()">测试登录按钮绑定</button>
            <button class="test-button" onclick="testFormValidation()">测试表单验证</button>
            <button class="test-button" onclick="testLoadingState()">测试加载状态</button>
            <button class="test-button" onclick="testErrorHandling()">测试错误处理</button>
            
            <div class="test-results" id="basicTestResults">
                <p>点击上方按钮开始测试...</p>
            </div>
        </div>
        
        <div class="test-section">
            <h2>登录流程测试</h2>
            <button class="test-button" onclick="testValidLogin()">测试有效登录</button>
            <button class="test-button" onclick="testInvalidLogin()">测试无效登录</button>
            <button class="test-button" onclick="testLocalModeLogin()">测试本地模式登录</button>
            <button class="test-button" onclick="testNetworkError()">测试网络错误</button>
            
            <div class="test-results" id="loginTestResults">
                <p>点击上方按钮开始登录测试...</p>
            </div>
        </div>
        
        <div class="test-section">
            <h2>性能和用户体验测试</h2>
            <button class="test-button" onclick="testResponseTime()">测试响应时间</button>
            <button class="test-button" onclick="testRetryMechanism()">测试重试机制</button>
            <button class="test-button" onclick="testUserFeedback()">测试用户反馈</button>
            <button class="test-button" onclick="runFullTest()">运行完整测试</button>
            
            <div class="test-results" id="performanceTestResults">
                <p>点击上方按钮开始性能测试...</p>
            </div>
        </div>
        
        <div class="test-section">
            <h2>测试日志</h2>
            <button class="test-button" onclick="clearLog()">清空日志</button>
            <button class="test-button" onclick="exportLog()">导出日志</button>
            
            <div class="test-log" id="testLog">
                <p>测试日志将显示在这里...</p>
            </div>
        </div>
    </div>

    <script>
        // 测试工具类
        class LoginTestTool {
            constructor() {
                this.testResults = [];
                this.startTime = null;
            }
            
            log(message, type = 'info') {
                const timestamp = new Date().toLocaleTimeString();
                const logEntry = `[${timestamp}] ${type.toUpperCase()}: ${message}`;
                
                this.testResults.push({ timestamp, message, type });
                
                const logElement = document.getElementById('testLog');
                const logLine = document.createElement('div');
                logLine.style.color = this.getLogColor(type);
                logLine.textContent = logEntry;
                logElement.appendChild(logLine);
                logElement.scrollTop = logElement.scrollHeight;
                
                console.log(logEntry);
            }
            
            getLogColor(type) {
                const colors = {
                    'info': '#333',
                    'success': '#28a745',
                    'error': '#dc3545',
                    'warning': '#ffc107'
                };
                return colors[type] || '#333';
            }
            
            updateResults(sectionId, content) {
                const section = document.getElementById(sectionId);
                section.innerHTML = content;
            }
            
            createStatusIndicator(status) {
                return `<span class="status-indicator status-${status}"></span>`;
            }
            
            async simulateDelay(ms) {
                return new Promise(resolve => setTimeout(resolve, ms));
            }
        }
        
        const testTool = new LoginTestTool();
        
        // 基础功能测试
        async function testLoginButtonBinding() {
            testTool.log('开始测试登录按钮绑定');
            
            try {
                // 检查登录表单是否存在
                const loginForm = parent.document.getElementById('loginForm');
                const loginButton = parent.document.querySelector('#loginForm button[type="submit"]');
                
                let results = '<h3>登录按钮绑定测试结果</h3>';
                
                if (loginForm) {
                    results += `${testTool.createStatusIndicator('success')} 登录表单存在<br>`;
                    testTool.log('登录表单检查通过', 'success');
                } else {
                    results += `${testTool.createStatusIndicator('error')} 登录表单不存在<br>`;
                    testTool.log('登录表单检查失败', 'error');
                }
                
                if (loginButton) {
                    results += `${testTool.createStatusIndicator('success')} 登录按钮存在<br>`;
                    testTool.log('登录按钮检查通过', 'success');
                } else {
                    results += `${testTool.createStatusIndicator('error')} 登录按钮不存在<br>`;
                    testTool.log('登录按钮检查失败', 'error');
                }
                
                // 检查事件监听器
                const hasEventListener = parent.window.enhancedLoginHandler ? true : false;
                if (hasEventListener) {
                    results += `${testTool.createStatusIndicator('success')} 增强登录处理器已加载<br>`;
                    testTool.log('增强登录处理器检查通过', 'success');
                } else {
                    results += `${testTool.createStatusIndicator('warning')} 增强登录处理器未加载<br>`;
                    testTool.log('增强登录处理器检查失败', 'warning');
                }
                
                testTool.updateResults('basicTestResults', results);
                
            } catch (error) {
                testTool.log(`登录按钮绑定测试失败: ${error.message}`, 'error');
                testTool.updateResults('basicTestResults', `${testTool.createStatusIndicator('error')} 测试失败: ${error.message}`);
            }
        }
        
        async function testFormValidation() {
            testTool.log('开始测试表单验证');
            
            try {
                let results = '<h3>表单验证测试结果</h3>';
                
                // 测试空邮箱验证
                const emptyEmailTest = await testValidationCase('', 'password123', '空邮箱');
                results += `${testTool.createStatusIndicator(emptyEmailTest.success ? 'success' : 'error')} ${emptyEmailTest.message}<br>`;
                
                // 测试空密码验证
                const emptyPasswordTest = await testValidationCase('<EMAIL>', '', '空密码');
                results += `${testTool.createStatusIndicator(emptyPasswordTest.success ? 'success' : 'error')} ${emptyPasswordTest.message}<br>`;
                
                // 测试无效邮箱格式
                const invalidEmailTest = await testValidationCase('invalid-email', 'password123', '无效邮箱格式');
                results += `${testTool.createStatusIndicator(invalidEmailTest.success ? 'success' : 'error')} ${invalidEmailTest.message}<br>`;
                
                testTool.updateResults('basicTestResults', results);
                
            } catch (error) {
                testTool.log(`表单验证测试失败: ${error.message}`, 'error');
            }
        }
        
        async function testValidationCase(email, password, testName) {
            try {
                if (parent.window.enhancedLoginHandler) {
                    const validation = parent.window.enhancedLoginHandler.validateFormData({ email, password });
                    const expected = (email === '' || password === '' || !email.includes('@'));
                    const success = expected ? !validation.valid : validation.valid;
                    
                    testTool.log(`${testName}验证测试: ${success ? '通过' : '失败'}`, success ? 'success' : 'error');
                    return { success, message: `${testName}验证: ${success ? '通过' : '失败'}` };
                } else {
                    return { success: false, message: `${testName}验证: 处理器未加载` };
                }
            } catch (error) {
                return { success: false, message: `${testName}验证: 错误 - ${error.message}` };
            }
        }
        
        async function testLoadingState() {
            testTool.log('开始测试加载状态');
            
            try {
                const loginButton = parent.document.querySelector('#loginForm button[type="submit"]');
                
                if (!loginButton) {
                    throw new Error('登录按钮不存在');
                }
                
                // 模拟显示加载状态
                const originalText = loginButton.innerHTML;
                loginButton.disabled = true;
                loginButton.innerHTML = '<div class="loading"></div> 登录中...';
                
                await testTool.simulateDelay(2000);
                
                // 恢复原状态
                loginButton.disabled = false;
                loginButton.innerHTML = originalText;
                
                const results = `
                    <h3>加载状态测试结果</h3>
                    ${testTool.createStatusIndicator('success')} 加载状态显示正常<br>
                    ${testTool.createStatusIndicator('success')} 按钮禁用/启用正常<br>
                    ${testTool.createStatusIndicator('success')} 状态恢复正常
                `;
                
                testTool.updateResults('basicTestResults', results);
                testTool.log('加载状态测试完成', 'success');
                
            } catch (error) {
                testTool.log(`加载状态测试失败: ${error.message}`, 'error');
            }
        }
        
        async function testErrorHandling() {
            testTool.log('开始测试错误处理');
            
            try {
                const errorDiv = parent.document.getElementById('loginError');
                
                if (!errorDiv) {
                    throw new Error('错误显示元素不存在');
                }
                
                // 模拟显示错误
                errorDiv.textContent = '测试错误消息';
                errorDiv.style.display = 'block';
                
                await testTool.simulateDelay(2000);
                
                // 清除错误
                errorDiv.style.display = 'none';
                errorDiv.textContent = '';
                
                const results = `
                    <h3>错误处理测试结果</h3>
                    ${testTool.createStatusIndicator('success')} 错误消息显示正常<br>
                    ${testTool.createStatusIndicator('success')} 错误消息清除正常
                `;
                
                testTool.updateResults('basicTestResults', results);
                testTool.log('错误处理测试完成', 'success');
                
            } catch (error) {
                testTool.log(`错误处理测试失败: ${error.message}`, 'error');
            }
        }
        
        // 登录流程测试
        async function testValidLogin() {
            testTool.log('开始测试有效登录');
            
            const [email, password] = document.getElementById('testAccount').value.split('|');
            
            try {
                // 填充表单
                const emailInput = parent.document.getElementById('email');
                const passwordInput = parent.document.getElementById('password');
                
                if (emailInput && passwordInput) {
                    emailInput.value = email;
                    passwordInput.value = password;
                    
                    testTool.log(`使用账号: ${email}`, 'info');
                    
                    // 模拟登录（这里只是测试UI，不实际登录）
                    const results = `
                        <h3>有效登录测试结果</h3>
                        ${testTool.createStatusIndicator('success')} 表单填充成功<br>
                        ${testTool.createStatusIndicator('info')} 测试账号: ${email}<br>
                        ${testTool.createStatusIndicator('warning')} 注意: 这是UI测试，未实际登录
                    `;
                    
                    testTool.updateResults('loginTestResults', results);
                    testTool.log('有效登录测试完成', 'success');
                } else {
                    throw new Error('登录表单元素不存在');
                }
                
            } catch (error) {
                testTool.log(`有效登录测试失败: ${error.message}`, 'error');
            }
        }
        
        async function testLocalModeLogin() {
            testTool.log('开始测试本地模式登录');
            
            try {
                const isLocalMode = parent.window.localDataProvider?.isLocalModeEnabled();
                
                const results = `
                    <h3>本地模式登录测试结果</h3>
                    ${testTool.createStatusIndicator(isLocalMode ? 'success' : 'warning')} 本地模式状态: ${isLocalMode ? '已启用' : '未启用'}<br>
                    ${testTool.createStatusIndicator('info')} 本地数据提供器: ${parent.window.localDataProvider ? '已加载' : '未加载'}
                `;
                
                testTool.updateResults('loginTestResults', results);
                testTool.log(`本地模式状态: ${isLocalMode ? '已启用' : '未启用'}`, isLocalMode ? 'success' : 'warning');
                
            } catch (error) {
                testTool.log(`本地模式测试失败: ${error.message}`, 'error');
            }
        }
        
        async function runFullTest() {
            testTool.log('开始运行完整测试套件', 'info');
            
            const tests = [
                { name: '登录按钮绑定', func: testLoginButtonBinding },
                { name: '表单验证', func: testFormValidation },
                { name: '加载状态', func: testLoadingState },
                { name: '错误处理', func: testErrorHandling },
                { name: '本地模式', func: testLocalModeLogin }
            ];
            
            let passedTests = 0;
            
            for (const test of tests) {
                testTool.log(`执行测试: ${test.name}`, 'info');
                try {
                    await test.func();
                    passedTests++;
                    await testTool.simulateDelay(500);
                } catch (error) {
                    testTool.log(`测试 ${test.name} 失败: ${error.message}`, 'error');
                }
            }
            
            const results = `
                <h3>完整测试结果</h3>
                ${testTool.createStatusIndicator('info')} 总测试数: ${tests.length}<br>
                ${testTool.createStatusIndicator('success')} 通过测试: ${passedTests}<br>
                ${testTool.createStatusIndicator(passedTests === tests.length ? 'success' : 'warning')} 成功率: ${Math.round(passedTests / tests.length * 100)}%
            `;
            
            testTool.updateResults('performanceTestResults', results);
            testTool.log(`完整测试完成，成功率: ${Math.round(passedTests / tests.length * 100)}%`, 'success');
        }
        
        function clearLog() {
            document.getElementById('testLog').innerHTML = '<p>测试日志已清空...</p>';
            testTool.testResults = [];
            testTool.log('日志已清空', 'info');
        }
        
        function exportLog() {
            const logData = testTool.testResults.map(entry => 
                `[${entry.timestamp}] ${entry.type.toUpperCase()}: ${entry.message}`
            ).join('\n');
            
            const blob = new Blob([logData], { type: 'text/plain' });
            const url = URL.createObjectURL(blob);
            const a = document.createElement('a');
            a.href = url;
            a.download = `login-test-log-${new Date().toISOString().slice(0, 19).replace(/:/g, '-')}.txt`;
            a.click();
            URL.revokeObjectURL(url);
            
            testTool.log('日志已导出', 'success');
        }
        
        // 页面加载完成后的初始化
        document.addEventListener('DOMContentLoaded', () => {
            testTool.log('登录功能测试工具已加载', 'success');
            testTool.log('请选择测试项目开始测试', 'info');
        });
    </script>
</body>
</html>
