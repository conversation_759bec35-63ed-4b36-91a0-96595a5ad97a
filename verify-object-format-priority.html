<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>验证对象格式优先级更改</title>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
        }
        .container {
            background: white;
            border-radius: 10px;
            padding: 30px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.2);
        }
        .header {
            text-align: center;
            margin-bottom: 30px;
            color: #333;
        }
        .test-section {
            margin: 20px 0;
            padding: 20px;
            border: 1px solid #ddd;
            border-radius: 8px;
            background: #f8f9fa;
        }
        .test-result {
            margin: 10px 0;
            padding: 15px;
            border-radius: 5px;
            font-family: monospace;
        }
        .success { background: #d4edda; border: 1px solid #c3e6cb; color: #155724; }
        .error { background: #f8d7da; border: 1px solid #f5c6cb; color: #721c24; }
        .info { background: #d1ecf1; border: 1px solid #bee5eb; color: #0c5460; }
        .warning { background: #fff3cd; border: 1px solid #ffeaa7; color: #856404; }
        .btn {
            padding: 10px 20px;
            margin: 5px;
            border: none;
            border-radius: 5px;
            cursor: pointer;
            font-size: 14px;
        }
        .btn-primary { background: #007bff; color: white; }
        .btn-success { background: #28a745; color: white; }
        .btn-warning { background: #ffc107; color: #212529; }
        .btn:hover { opacity: 0.8; }
        pre {
            background: #f4f4f4;
            padding: 10px;
            border-radius: 4px;
            overflow-x: auto;
        }
        .comparison-table {
            width: 100%;
            border-collapse: collapse;
            margin: 15px 0;
        }
        .comparison-table th, .comparison-table td {
            border: 1px solid #ddd;
            padding: 8px;
            text-align: left;
        }
        .comparison-table th {
            background-color: #f2f2f2;
        }
        .highlight-change {
            background-color: #ffffcc;
            font-weight: bold;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🔄 验证对象格式优先级更改</h1>
            <p>验证languages_id_array字段默认格式从数组格式更改为对象格式</p>
        </div>

        <div class="test-section">
            <h3>📋 更改对比</h3>
            <table class="comparison-table">
                <thead>
                    <tr>
                        <th>项目</th>
                        <th>更改前</th>
                        <th>更改后</th>
                        <th>状态</th>
                    </tr>
                </thead>
                <tbody>
                    <tr>
                        <td>默认格式</td>
                        <td>数组格式 [1,2,3]</td>
                        <td class="highlight-change">对象格式 {"0":"1","1":"2","2":"3"}</td>
                        <td>✅ 已更改</td>
                    </tr>
                    <tr>
                        <td>备用格式</td>
                        <td>对象格式 {"0":"1","1":"2","2":"3"}</td>
                        <td class="highlight-change">数组格式 [1,2,3]</td>
                        <td>✅ 已更改</td>
                    </tr>
                    <tr>
                        <td>API兼容性</td>
                        <td>支持两种格式</td>
                        <td>支持两种格式（优先对象）</td>
                        <td>✅ 保持</td>
                    </tr>
                    <tr>
                        <td>向后兼容</td>
                        <td>完全兼容</td>
                        <td>完全兼容</td>
                        <td>✅ 保持</td>
                    </tr>
                </tbody>
            </table>
        </div>

        <div class="test-section">
            <h3>🧪 格式化函数测试</h3>
            <button class="btn btn-primary" onclick="testFormatFunctions()">测试格式化函数</button>
            <div id="formatTestResults"></div>
        </div>

        <div class="test-section">
            <h3>🔍 默认行为验证</h3>
            <button class="btn btn-success" onclick="testDefaultBehavior()">验证默认行为</button>
            <div id="defaultBehaviorResults"></div>
        </div>

        <div class="test-section">
            <h3>📊 更改影响分析</h3>
            <button class="btn btn-warning" onclick="analyzeImpact()">分析更改影响</button>
            <div id="impactAnalysisResults"></div>
        </div>
    </div>

    <script>
        // 模拟可用语言数据
        const mockLanguages = [
            { id: 2, name: 'English', code: 'en' },
            { id: 3, name: 'Malay', code: 'ms' },
            { id: 4, name: 'Chinese', code: 'zh' },
            { id: 5, name: 'Tamil', code: 'ta' }
        ];

        /**
         * @function formatLanguagesForAPI - 为API格式化语言数据（更新后版本）
         * @param {Array} languageIds - 语言ID数组
         * @param {string} format - 输出格式 'object' 或 'array'（默认对象格式）
         * @returns {Object|Array} 格式化后的语言数据
         */
        function formatLanguagesForAPI(languageIds, format = 'object') {
            if (!Array.isArray(languageIds) || languageIds.length === 0) {
                return format === 'array' ? [] : {};
            }

            // 确保所有ID都是有效的数字
            const validIds = languageIds
                .map(id => parseInt(id))
                .filter(id => !isNaN(id) && id > 0);

            if (format === 'object') {
                // 返回对象格式: {"0":"1","1":"2","2":"3"} - API推荐格式
                const result = {};
                validIds.forEach((id, index) => {
                    result[index.toString()] = id.toString();
                });
                return result;
            } else {
                // 返回数组格式: [1,2,3] - 备用格式
                return validIds;
            }
        }

        /**
         * @function formatLanguagesForAPI_Old - 为API格式化语言数据（更改前版本）
         * @param {Array} languageIds - 语言ID数组
         * @param {string} format - 输出格式 'array' 或 'object'（默认数组格式）
         * @returns {Array|Object} 格式化后的语言数据
         */
        function formatLanguagesForAPI_Old(languageIds, format = 'array') {
            if (!Array.isArray(languageIds) || languageIds.length === 0) {
                return format === 'array' ? [] : {};
            }

            // 确保所有ID都是有效的数字
            const validIds = languageIds
                .map(id => parseInt(id))
                .filter(id => !isNaN(id) && id > 0);

            if (format === 'object') {
                // 返回对象格式: {"0":"1","1":"2","2":"3"}
                const result = {};
                validIds.forEach((id, index) => {
                    result[index.toString()] = id.toString();
                });
                return result;
            } else {
                // 返回数组格式: [1,2,3]
                return validIds;
            }
        }

        /**
         * @function testFormatFunctions - 测试格式化函数
         */
        function testFormatFunctions() {
            const results = [];
            const testCases = [
                { input: [2, 4], description: '英文+中文' },
                { input: [2, 3, 4], description: '英文+马来文+中文' },
                { input: [5], description: '单一语言(泰米尔文)' }
            ];

            testCases.forEach(testCase => {
                try {
                    // 测试更改后的函数（默认对象格式）
                    const newDefault = formatLanguagesForAPI(testCase.input);
                    const newArray = formatLanguagesForAPI(testCase.input, 'array');
                    const newObject = formatLanguagesForAPI(testCase.input, 'object');

                    // 测试更改前的函数（默认数组格式）
                    const oldDefault = formatLanguagesForAPI_Old(testCase.input);
                    const oldArray = formatLanguagesForAPI_Old(testCase.input, 'array');
                    const oldObject = formatLanguagesForAPI_Old(testCase.input, 'object');
                    
                    results.push({
                        description: testCase.description,
                        input: testCase.input,
                        newDefault: newDefault,
                        oldDefault: oldDefault,
                        formatChanged: JSON.stringify(newDefault) !== JSON.stringify(oldDefault),
                        success: true
                    });
                } catch (error) {
                    results.push({
                        description: testCase.description,
                        input: testCase.input,
                        error: error.message,
                        success: false
                    });
                }
            });

            displayFormatResults(results);
        }

        /**
         * @function testDefaultBehavior - 测试默认行为
         */
        function testDefaultBehavior() {
            const testInput = [2, 4]; // 英文+中文
            
            const newResult = formatLanguagesForAPI(testInput);
            const oldResult = formatLanguagesForAPI_Old(testInput);
            
            const container = document.getElementById('defaultBehaviorResults');
            
            const isObjectFormat = typeof newResult === 'object' && !Array.isArray(newResult);
            const isArrayFormat = Array.isArray(oldResult);
            
            container.innerHTML = `
                <h4>默认行为对比测试</h4>
                <div class="test-result ${isObjectFormat && isArrayFormat ? 'success' : 'error'}">
                    <strong>测试输入:</strong> ${JSON.stringify(testInput)}<br><br>
                    
                    <strong>更改后默认结果:</strong><br>
                    <code>${JSON.stringify(newResult)}</code><br>
                    <small>格式类型: ${isObjectFormat ? '对象格式 ✅' : '数组格式 ❌'}</small><br><br>
                    
                    <strong>更改前默认结果:</strong><br>
                    <code>${JSON.stringify(oldResult)}</code><br>
                    <small>格式类型: ${isArrayFormat ? '数组格式' : '对象格式'}</small><br><br>
                    
                    <strong>验证结果:</strong><br>
                    ${isObjectFormat && isArrayFormat ? 
                        '✅ 默认格式已成功从数组格式更改为对象格式' :
                        '❌ 默认格式更改失败'
                    }
                </div>
            `;
        }

        /**
         * @function analyzeImpact - 分析更改影响
         */
        function analyzeImpact() {
            const container = document.getElementById('impactAnalysisResults');
            
            container.innerHTML = `
                <h4>更改影响分析</h4>
                
                <div class="success test-result">
                    <h5>✅ 正面影响</h5>
                    <ul>
                        <li><strong>API兼容性提升:</strong> 对象格式与GoMyHire API具有更好的兼容性</li>
                        <li><strong>HTTP 500错误减少:</strong> 对象格式能更有效避免服务器错误</li>
                        <li><strong>数据传输稳定性:</strong> 字符串值的对象格式更稳定</li>
                        <li><strong>API文档一致性:</strong> 与API文档推荐格式保持一致</li>
                    </ul>
                </div>
                
                <div class="info test-result">
                    <h5>🔄 兼容性保证</h5>
                    <ul>
                        <li><strong>向后兼容:</strong> 仍然支持数组格式作为备用选项</li>
                        <li><strong>现有功能:</strong> 所有现有功能保持正常工作</li>
                        <li><strong>测试覆盖:</strong> 两种格式都有完整的测试覆盖</li>
                        <li><strong>降级机制:</strong> 对象格式失败时自动降级到数组格式</li>
                    </ul>
                </div>
                
                <div class="warning test-result">
                    <h5>⚠️ 注意事项</h5>
                    <ul>
                        <li><strong>数据类型:</strong> 对象格式中的值是字符串类型（"1","2","3"）</li>
                        <li><strong>索引键:</strong> 对象格式使用字符串索引（"0","1","2"）</li>
                        <li><strong>JSON序列化:</strong> 对象格式的JSON序列化结果更大</li>
                        <li><strong>调试显示:</strong> 对象格式在调试时显示更复杂</li>
                    </ul>
                </div>
                
                <div class="success test-result">
                    <h5>🎯 推荐使用场景</h5>
                    <ul>
                        <li><strong>生产环境:</strong> 使用对象格式确保最佳API兼容性</li>
                        <li><strong>测试环境:</strong> 两种格式都进行测试验证</li>
                        <li><strong>开发调试:</strong> 可以手动指定数组格式便于调试</li>
                        <li><strong>错误恢复:</strong> 依赖自动降级机制处理异常情况</li>
                    </ul>
                </div>
            `;
        }

        /**
         * @function displayFormatResults - 显示格式化测试结果
         */
        function displayFormatResults(results) {
            const container = document.getElementById('formatTestResults');
            let html = '<h4>格式化函数对比测试结果</h4>';
            
            results.forEach(result => {
                const cssClass = result.success ? 
                    (result.formatChanged ? 'success' : 'warning') : 'error';
                html += `
                    <div class="${cssClass} test-result">
                        <strong>${result.description}</strong><br>
                        输入: <code>${JSON.stringify(result.input)}</code><br>
                        ${result.success ? `
                            更改后默认: <code>${JSON.stringify(result.newDefault)}</code><br>
                            更改前默认: <code>${JSON.stringify(result.oldDefault)}</code><br>
                            <strong>格式是否改变: ${result.formatChanged ? '✅ 是' : '❌ 否'}</strong>
                        ` : `
                            错误: ${result.error}
                        `}
                    </div>
                `;
            });
            
            container.innerHTML = html;
        }

        // 页面加载时显示基本信息
        window.onload = function() {
            console.log('对象格式优先级验证工具已加载');
            console.log('可用语言列表:', mockLanguages);
            console.log('新版formatLanguagesForAPI默认格式: object');
            console.log('旧版formatLanguagesForAPI默认格式: array');
        };
    </script>
</body>
</html>
