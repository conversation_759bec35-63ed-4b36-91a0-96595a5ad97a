<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>GoMyHire API 多账号测试工具</title>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            margin: 0;
            padding: 20px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
        }
        .container {
            max-width: 1400px;
            margin: 0 auto;
            background: white;
            border-radius: 10px;
            padding: 30px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.2);
        }
        .header {
            text-align: center;
            margin-bottom: 30px;
            color: #333;
        }
        .account-selector {
            background: #f8f9fa;
            border: 1px solid #dee2e6;
            border-radius: 8px;
            padding: 20px;
            margin-bottom: 20px;
        }
        .account-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 15px;
            margin-top: 15px;
        }
        .account-card {
            background: white;
            border: 2px solid #e9ecef;
            border-radius: 8px;
            padding: 15px;
            cursor: pointer;
            transition: all 0.3s ease;
        }
        .account-card:hover {
            border-color: #007bff;
            box-shadow: 0 4px 12px rgba(0,123,255,0.15);
        }
        .account-card.active {
            border-color: #28a745;
            background: #f8fff9;
        }
        .account-card.authenticated {
            border-color: #28a745;
            background: #d4edda;
        }
        .data-source-indicator {
            display: inline-block;
            padding: 4px 8px;
            border-radius: 4px;
            font-size: 12px;
            font-weight: bold;
            margin-left: 10px;
        }
        .data-source-api {
            background: #d4edda;
            color: #155724;
        }
        .data-source-static {
            background: #fff3cd;
            color: #856404;
        }
        .test-section {
            background: #f8f9fa;
            border: 1px solid #dee2e6;
            border-radius: 8px;
            padding: 20px;
            margin-bottom: 20px;
        }
        .btn {
            padding: 10px 20px;
            margin: 5px;
            border: none;
            border-radius: 5px;
            cursor: pointer;
            font-size: 14px;
            transition: all 0.3s ease;
        }
        .btn:hover { opacity: 0.8; }
        .btn-primary { background: #007bff; color: white; }
        .btn-success { background: #28a745; color: white; }
        .btn-warning { background: #ffc107; color: #212529; }
        .btn-danger { background: #dc3545; color: white; }
        .btn-secondary { background: #6c757d; color: white; }
        .btn-info { background: #17a2b8; color: white; }
        .test-result {
            margin: 10px 0;
            padding: 15px;
            border-radius: 5px;
            font-family: monospace;
            display: none;
        }
        .result-success { background: #d4edda; border: 1px solid #c3e6cb; color: #155724; }
        .result-error { background: #f8d7da; border: 1px solid #f5c6cb; color: #721c24; }
        .result-pending { background: #d1ecf1; border: 1px solid #bee5eb; color: #0c5460; }
        .result-warning { background: #fff3cd; border: 1px solid #ffeaa7; color: #856404; }
        .form-group {
            margin-bottom: 15px;
        }
        .form-group label {
            display: block;
            margin-bottom: 5px;
            font-weight: bold;
        }
        .form-control {
            width: 100%;
            padding: 8px 12px;
            border: 1px solid #ced4da;
            border-radius: 4px;
            font-size: 14px;
        }
        .grid-2 {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 20px;
        }
        .grid-3 {
            display: grid;
            grid-template-columns: 1fr 1fr 1fr;
            gap: 20px;
        }
        .status-indicator {
            display: inline-block;
            width: 12px;
            height: 12px;
            border-radius: 50%;
            margin-right: 8px;
        }
        .status-success { background: #28a745; }
        .status-error { background: #dc3545; }
        .status-pending { background: #ffc107; }
        .comparison-table {
            width: 100%;
            border-collapse: collapse;
            margin: 15px 0;
        }
        .comparison-table th, .comparison-table td {
            border: 1px solid #ddd;
            padding: 8px;
            text-align: left;
        }
        .comparison-table th {
            background-color: #f2f2f2;
        }
        pre {
            background: #f4f4f4;
            padding: 10px;
            border-radius: 4px;
            overflow-x: auto;
            max-height: 300px;
        }
        .collapsible {
            cursor: pointer;
            padding: 10px;
            background: #e9ecef;
            border: none;
            text-align: left;
            outline: none;
            font-size: 14px;
            border-radius: 4px;
            margin: 5px 0;
            width: 100%;
        }
        .collapsible:hover {
            background: #dee2e6;
        }
        .collapsible-content {
            padding: 0 15px;
            display: none;
            overflow: hidden;
            background: #f8f9fa;
            border-radius: 4px;
            margin-bottom: 10px;
        }
        .account-info {
            font-size: 12px;
            color: #6c757d;
            margin-top: 5px;
        }
        .backend-user-info {
            background: #e3f2fd;
            border: 1px solid #bbdefb;
            border-radius: 4px;
            padding: 10px;
            margin: 10px 0;
            font-size: 12px;
        }
        .batch-progress {
            margin: 15px 0;
        }
        .batch-progress-container {
            margin: 10px 0;
        }
        .batch-progress-bg {
            background: #e9ecef;
            border-radius: 10px;
            overflow: hidden;
        }
        .batch-progress-bar {
            background: #007bff;
            height: 20px;
            width: 0%;
            transition: width 0.3s ease;
        }
        .batch-progress-text {
            text-align: center;
            margin-top: 5px;
        }
        .batch-stats {
            margin-top: 10px;
        }
        .batch-stat-item {
            text-align: center;
        }
        .batch-success {
            color: #28a745;
        }
        .batch-fail {
            color: #dc3545;
        }
        .random-data-container {
            background: #f8f9fa;
            border: 1px solid #dee2e6;
            border-radius: 4px;
            padding: 10px;
            margin: 10px 0;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🔧 GoMyHire API 多账号测试工具</h1>
            <p>支持多账号切换、智能后台用户映射、API数据获取与降级机制</p>
        </div>

        <!-- 账号选择器 -->
        <div class="account-selector">
            <h3>📧 账号选择与认证</h3>
            <div class="account-grid" id="accountGrid">
                <!-- 账号卡片将通过JavaScript动态生成 -->
            </div>
            <div class="backend-user-info" id="backendUserInfo" style="display: none;">
                <!-- 后台用户信息将在这里显示 -->
            </div>
        </div>

        <!-- 数据状态指示器 -->
        <div class="test-section">
            <h3>📊 数据源状态</h3>
            <div class="grid-2">
                <div>
                    <strong>后台用户:</strong>
                    <span id="backendUsersStatus">未加载</span>
                    <span id="backendUsersSource" class="data-source-indicator">静态</span>
                </div>
                <div>
                    <strong>车型数据:</strong>
                    <span id="carTypesStatus">未加载</span>
                    <span id="carTypesSource" class="data-source-indicator">静态</span>
                </div>
                <div>
                    <strong>子分类:</strong>
                    <span id="subCategoriesStatus">未加载</span>
                    <span id="subCategoriesSource" class="data-source-indicator">静态</span>
                </div>
                <div>
                    <strong>语言数据:</strong>
                    <span id="languagesStatus">未加载</span>
                    <span id="languagesSource" class="data-source-indicator">静态</span>
                </div>
            </div>
        </div>

        <!-- 核心测试功能 -->
        <div class="test-section">
            <h3>🧪 核心测试功能</h3>
            <div class="grid-3">
                <button type="button" class="btn btn-primary" onclick="testAuthentication()">🔐 测试认证</button>
                <button type="button" class="btn btn-success" onclick="testDataLoading()">📊 测试数据加载</button>
                <button type="button" class="btn btn-warning" onclick="testFallbackMechanism()">📦 测试降级机制</button>
                <button type="button" class="btn btn-info" onclick="testConcurrentLoading()">⚡ 测试并发加载</button>
                <button type="button" class="btn btn-secondary" onclick="testLanguagesArrayFormat()">🗣️ 测试语言格式</button>
                <button type="button" class="btn btn-danger" onclick="simulateAPIFailure()">❌ 模拟API失败</button>
            </div>
            <div class="grid-2" style="margin-top: 10px;">
                <button type="button" class="btn btn-info" onclick="testEndpointHealth()">🏥 端点健康检查</button>
                <button type="button" class="btn btn-warning" onclick="testResponseStructure()">🔍 响应结构分析</button>
            </div>
            <div class="grid-2" style="margin-top: 10px;">
                <button type="button" class="btn btn-info" onclick="testAlternativeLoginEndpoints()">🔄 测试备用登录端点</button>
                <button type="button" class="btn btn-warning" onclick="quickAuthFix()">⚡ 快速认证修复</button>
            </div>
            <div id="coreTestResults" class="test-result"></div>
        </div>

        <!-- 多账号对比测试 -->
        <div class="test-section">
            <h3>👥 多账号对比测试</h3>
            <div class="grid-2">
                <button type="button" class="btn btn-info" onclick="runMultiAccountTest()">🔄 运行多账号测试</button>
                <button type="button" class="btn btn-warning" onclick="compareAccountResults()">📊 对比测试结果</button>
            </div>
            <div id="multiAccountResults" class="test-result"></div>
        </div>

        <!-- 手动订单测试 -->
        <div class="test-section">
            <h3>📝 手动订单测试</h3>
            <div class="grid-2">
                <div class="form-group">
                    <label for="testOrderType">订单类型:</label>
                    <select id="testOrderType" class="form-control">
                        <option value="pickup">接机服务</option>
                        <option value="dropoff">送机服务</option>
                        <option value="charter">包车服务</option>
                    </select>
                </div>
                <div class="form-group">
                    <label for="testPassengerCount">乘客人数:</label>
                    <input type="number" id="testPassengerCount" class="form-control" value="2" min="1" max="50">
                </div>
            </div>
            <div class="grid-2">
                <div class="form-group">
                    <label for="testPickup">接机地点:</label>
                    <input type="text" id="testPickup" class="form-control" value="KLIA Airport">
                </div>
                <div class="form-group">
                    <label for="testDestination">目的地:</label>
                    <input type="text" id="testDestination" class="form-control" value="KLCC">
                </div>
            </div>
            <div class="grid-3">
                <div class="form-group">
                    <label for="testCustomerName">客户姓名:</label>
                    <input type="text" id="testCustomerName" class="form-control" value="张三">
                </div>
                <div class="form-group">
                    <label for="testCustomerPhone">客户电话:</label>
                    <input type="text" id="testCustomerPhone" class="form-control" value="+60123456789">
                </div>
                <div class="form-group">
                    <label for="testCustomerEmail">客户邮箱:</label>
                    <input type="text" id="testCustomerEmail" class="form-control" value="<EMAIL>">
                </div>
            </div>
            <div class="grid-2">
                <div class="form-group">
                    <label for="testPickupDate">接机日期 (DD-MM-YYYY):</label>
                    <input type="text" id="testPickupDate" class="form-control" value="25-12-2024">
                </div>
                <div class="form-group">
                    <label for="testPickupTime">接机时间:</label>
                    <input type="text" id="testPickupTime" class="form-control" value="10:00">
                </div>
            </div>
            <div class="grid-3">
                <button type="button" class="btn btn-success" onclick="createManualTestOrder()">🚗 创建测试订单</button>
                <button type="button" class="btn btn-info" onclick="previewOrderData()">👁️ 预览订单数据</button>
                <button type="button" class="btn btn-warning" onclick="generateRandomTestData()">🎲 随机生成数据</button>
            </div>
            <div id="manualTestResults" class="test-result"></div>
        </div>

        <!-- 批量订单测试 -->
        <div class="test-section">
            <h3>🔄 批量订单测试</h3>
            <div class="grid-3">
                <div class="form-group">
                    <label for="batchOrderCount">批量订单数量:</label>
                    <input type="number" id="batchOrderCount" class="form-control" value="5" min="1" max="50">
                </div>
                <div class="form-group">
                    <label for="batchOrderDelay">订单间隔 (毫秒):</label>
                    <input type="number" id="batchOrderDelay" class="form-control" value="1000" min="100" max="5000">
                </div>
                <div class="form-group">
                    <label for="batchOrderType">批量订单类型:</label>
                    <select id="batchOrderType" class="form-control">
                        <option value="mixed">混合类型</option>
                        <option value="pickup">仅接机服务</option>
                        <option value="dropoff">仅送机服务</option>
                        <option value="charter">仅包车服务</option>
                    </select>
                </div>
            </div>
            <div class="grid-2">
                <button type="button" class="btn btn-primary" onclick="startBatchOrderTest()">🚀 开始批量测试</button>
                <button type="button" class="btn btn-danger" onclick="stopBatchOrderTest()">⏹️ 停止测试</button>
            </div>
            <div class="batch-progress" id="batchProgress" style="display: none;">
                <div class="batch-progress-container">
                    <div class="batch-progress-bg">
                        <div id="batchProgressBar" class="batch-progress-bar"></div>
                    </div>
                    <div class="batch-progress-text">
                        <span id="batchProgressText">准备中...</span>
                    </div>
                </div>
                <div class="grid-3 batch-stats">
                    <div class="batch-stat-item">
                        <strong>成功:</strong> <span id="batchSuccessCount" class="batch-success">0</span>
                    </div>
                    <div class="batch-stat-item">
                        <strong>失败:</strong> <span id="batchFailCount" class="batch-fail">0</span>
                    </div>
                    <div class="batch-stat-item">
                        <strong>总计:</strong> <span id="batchTotalCount">0</span>
                    </div>
                </div>
            </div>
            <div id="batchTestResults" class="test-result"></div>
        </div>

        <!-- API调用历史 -->
        <div class="test-section">
            <h3>📋 API调用历史</h3>
            <div class="grid-3">
                <button type="button" class="btn btn-info" onclick="showApiHistory()">📜 显示调用历史</button>
                <button type="button" class="btn btn-warning" onclick="exportApiLogs()">💾 导出日志</button>
                <button type="button" class="btn btn-danger" onclick="clearApiHistory()">🗑️ 清空历史</button>
            </div>
            <div id="apiHistoryResults" class="test-result"></div>
        </div>

        <!-- 错误诊断 -->
        <div class="test-section">
            <h3>🔍 错误诊断</h3>
            <div class="grid-3">
                <button type="button" class="btn btn-warning" onclick="diagnose500Error()">🚨 500错误专项诊断</button>
                <button type="button" class="btn btn-info" onclick="debugLastAPICall()">🔍 调试最后API调用</button>
                <button type="button" class="btn btn-danger" onclick="diagnoseAuthenticationIssues()">🔐 认证问题诊断</button>
            </div>
            <div id="diagnosticResults" class="test-result"></div>
        </div>
    </div>

    <script>
        /**
         * @file GoMyHire API 多账号测试工具
         * @description 支持多账号切换、智能后台用户映射、API数据获取与降级机制的综合测试工具
         */

        // ========== 全局配置和变量 ==========

        /**
         * @constant {Object} API_CONFIG - API配置
         */
        const API_CONFIG = {
            BASE_URL: 'https://gomyhire.com.my/api',
            ENDPOINTS: {
                login: '/login',
                create_order: '/create_order',
                backend_users: '/backend_users',
                car_types: '/car_types',
                sub_categories: '/sub_categories',
                languages: '/languages',
                regions: '/regions'
            }
        };

        /**
         * @constant {Array} TEST_ACCOUNTS - 预设测试账号
         */
        const TEST_ACCOUNTS = [
            {
                id: 'general',
                email: '<EMAIL>',
                password: 'Gomyhire@123456',
                name: 'General Admin',
                isDefault: true,
                backendUserId: null // 使用默认逻辑
            },
            {
                id: 'jcy',
                email: '<EMAIL>',
                password: 'Yap123',
                name: 'Jcy',
                isDefault: false,
                backendUserId: 310 // 固定映射到ID 310
            },
            {
                id: 'skymirror',
                email: '<EMAIL>',
                password: 'Sky@114788',
                name: 'Sky Mirror World',
                isDefault: false,
                backendUserId: 37 // 固定映射到ID 37
            }
        ];

        /**
         * @constant {Object} STATIC_DATA - 静态映射数据（从memory-bank/api return id list.md）
         */
        const STATIC_DATA = {
            backendUsers: [
                { id: 1, name: 'Super Admin', email: '' },
                { id: 37, name: 'smw', email: '<EMAIL>' },
                { id: 89, name: 'GMH Sabah', email: '<EMAIL>' },
                { id: 310, name: 'Jcy', email: '<EMAIL>' },
                { id: 311, name: 'opAnnie', email: '<EMAIL>' },
                { id: 312, name: 'opVenus', email: '<EMAIL>' },
                { id: 313, name: 'opEric', email: '' },
                { id: 342, name: 'SMW Wendy', email: 'SMW <EMAIL>' },
                { id: 343, name: 'SMW XiaoYu', email: 'SMW <EMAIL>' },
                { id: 420, name: 'chongyoonlim', email: '<EMAIL>' },
                { id: 421, name: 'josua', email: '<EMAIL>' }
            ],
            subCategories: [
                { id: 2, name: 'Pickup', type: 'pickup' },
                { id: 3, name: 'Dropoff', type: 'dropoff' },
                { id: 4, name: 'Charter', type: 'charter' },
                { id: 5, name: 'Paging', type: 'paging' },
                { id: 6, name: 'SIM Card Only', type: 'sim' }
            ],
            carTypes: [
                { id: 38, type: '4 Seater Hatchback', seat_number: 3, priority: 1 },
                { id: 5, type: '5 Seater', seat_number: 3, priority: 2 },
                { id: 33, type: 'Premium 5 Seater (Mercedes/BMW Only)', seat_number: 3, priority: 3 },
                { id: 37, type: 'Extended 5', seat_number: 4, priority: 4 },
                { id: 35, type: '7 Seater SUV', seat_number: 4, priority: 5 },
                { id: 15, type: '7 Seater MPV', seat_number: 5, priority: 6 },
                { id: 16, type: 'Standard Size MPV', seat_number: 5, priority: 7 },
                { id: 31, type: 'Luxury Mpv (Serena)', seat_number: 5, priority: 8 },
                { id: 32, type: 'Velfire/ Alphard', seat_number: 6, priority: 9 },
                { id: 36, type: 'Alphard', seat_number: 6, priority: 10 },
                { id: 20, type: '10 Seater MPV / Van', seat_number: 7, priority: 11 },
                { id: 30, type: '12 seat Starex', seat_number: 7, priority: 12 }
            ],
            languages: [
                { id: 2, name: 'English', code: 'EN' },
                { id: 3, name: 'Malay', code: 'MY' },
                { id: 4, name: 'Chinese', code: 'CN' },
                { id: 5, name: 'Paging', code: 'PG' }
            ],
            regions: [
                { id: 1, name: 'Kl/selangor', code: 'KL' },
                { id: 2, name: 'Penang', code: 'PNG' },
                { id: 3, name: 'Johor', code: 'JB' },
                { id: 4, name: 'Sabah', code: 'SBH' },
                { id: 5, name: 'Singapore', code: 'SG' }
            ]
        };

        // 全局状态变量
        let currentAccount = null;
        let authTokens = {}; // 存储每个账号的token
        let apiData = {
            backendUsers: [],
            carTypes: [],
            subCategories: [],
            languages: [],
            regions: []
        };
        let dataSource = {
            backendUsers: 'static',
            carTypes: 'static',
            subCategories: 'static',
            languages: 'static',
            regions: 'static'
        };
        let apiCallHistory = [];
        let testResults = {};

        // 批量测试状态变量
        let batchTestState = {
            isRunning: false,
            currentIndex: 0,
            totalCount: 0,
            successCount: 0,
            failCount: 0,
            orders: [],
            timeoutId: null
        };

        // ========== 初始化函数 ==========

        /**
         * @function initializeApp - 初始化应用
         * @description 页面加载时初始化所有组件
         */
        function initializeApp() {
            console.log('🚀 初始化GoMyHire API多账号测试工具...');

            // 渲染账号选择器
            renderAccountSelector();

            // 加载静态数据
            loadStaticData();

            // 更新数据源状态
            updateDataSourceStatus();

            // 设置默认账号
            const defaultAccount = TEST_ACCOUNTS.find(acc => acc.isDefault);
            if (defaultAccount) {
                selectAccount(defaultAccount.id);
            }

            console.log('✅ 应用初始化完成');
        }

        // ========== 账号管理函数 ==========

        /**
         * @function renderAccountSelector - 渲染账号选择器
         * @description 动态生成账号卡片界面
         */
        function renderAccountSelector() {
            const accountGrid = document.getElementById('accountGrid');
            accountGrid.innerHTML = '';

            TEST_ACCOUNTS.forEach(account => {
                const accountCard = document.createElement('div');
                accountCard.className = 'account-card';
                accountCard.id = `account-${account.id}`;
                accountCard.onclick = () => selectAccount(account.id);

                const isAuthenticated = authTokens[account.id] ? 'authenticated' : '';
                if (isAuthenticated) {
                    accountCard.classList.add('authenticated');
                }

                accountCard.innerHTML = `
                    <div style="display: flex; align-items: center; margin-bottom: 8px;">
                        <span class="status-indicator ${isAuthenticated ? 'status-success' : 'status-pending'}"></span>
                        <strong>${account.name}</strong>
                        ${account.isDefault ? '<span style="background: #007bff; color: white; padding: 2px 6px; border-radius: 3px; font-size: 10px; margin-left: 8px;">默认</span>' : ''}
                    </div>
                    <div class="account-info">
                        📧 ${account.email}<br>
                        🔑 ${account.password.replace(/./g, '*')}<br>
                        👤 后台用户ID: ${account.backendUserId || '自动选择'}
                    </div>
                `;

                accountGrid.appendChild(accountCard);
            });
        }

        /**
         * @function selectAccount - 选择账号
         * @param {string} accountId - 账号ID
         * @description 切换当前活跃账号并更新界面状态
         */
        function selectAccount(accountId) {
            // 移除之前的active状态
            document.querySelectorAll('.account-card').forEach(card => {
                card.classList.remove('active');
            });

            // 设置新的active状态
            const selectedCard = document.getElementById(`account-${accountId}`);
            if (selectedCard) {
                selectedCard.classList.add('active');
            }

            // 更新当前账号
            currentAccount = TEST_ACCOUNTS.find(acc => acc.id === accountId);

            // 更新后台用户信息显示
            updateBackendUserInfo();

            console.log(`🔄 切换到账号: ${currentAccount.name} (${currentAccount.email})`);
        }

        /**
         * @function updateBackendUserInfo - 更新后台用户信息显示
         * @description 显示当前账号对应的后台用户信息
         */
        function updateBackendUserInfo() {
            const backendUserInfo = document.getElementById('backendUserInfo');

            if (!currentAccount) {
                backendUserInfo.style.display = 'none';
                return;
            }

            let userInfo = '';
            if (currentAccount.backendUserId) {
                const user = apiData.backendUsers.find(u => u.id === currentAccount.backendUserId) ||
                            STATIC_DATA.backendUsers.find(u => u.id === currentAccount.backendUserId);
                if (user) {
                    userInfo = `固定映射: ID ${user.id} - ${user.name} (${user.email || '无邮箱'})`;
                } else {
                    userInfo = `固定映射: ID ${currentAccount.backendUserId} (数据未找到)`;
                }
            } else {
                userInfo = '使用默认逻辑: 将从API响应中自动选择第一个可用的后台用户';
            }

            backendUserInfo.innerHTML = `
                <strong>当前账号后台用户映射:</strong><br>
                ${userInfo}
            `;
            backendUserInfo.style.display = 'block';
        }

        // ========== 数据管理函数 ==========

        /**
         * @function loadStaticData - 加载静态数据
         * @description 从STATIC_DATA常量加载静态映射数据
         */
        function loadStaticData() {
            console.log('📦 加载静态映射数据...');

            // 复制静态数据到apiData
            Object.keys(STATIC_DATA).forEach(key => {
                apiData[key] = [...STATIC_DATA[key]];
                dataSource[key] = 'static';
            });

            console.log('✅ 静态数据加载完成');
        }

        /**
         * @function updateDataSourceStatus - 更新数据源状态显示
         * @description 更新界面上的数据源状态指示器
         */
        function updateDataSourceStatus() {
            Object.keys(dataSource).forEach(key => {
                const statusElement = document.getElementById(`${key}Status`);
                const sourceElement = document.getElementById(`${key}Source`);

                if (statusElement && sourceElement) {
                    const count = apiData[key] ? apiData[key].length : 0;
                    statusElement.textContent = `已加载 ${count} 条记录`;

                    sourceElement.textContent = dataSource[key] === 'api' ? '动态API' : '静态映射';
                    sourceElement.className = `data-source-indicator ${dataSource[key] === 'api' ? 'data-source-api' : 'data-source-static'}`;
                }
            });
        }

        // ========== API调用函数 ==========

        /**
         * @function makeAPICall - 执行API调用
         * @param {string} endpoint - API端点
         * @param {Object} data - 请求数据
         * @param {string} method - HTTP方法
         * @param {string} accountId - 账号ID
         * @returns {Promise<Object>} API响应
         * @description 统一的API调用函数，包含错误处理和日志记录
         */
        async function makeAPICall(endpoint, data = {}, method = 'POST', accountId = null) {
            const account = accountId ? TEST_ACCOUNTS.find(acc => acc.id === accountId) : currentAccount;
            if (!account) {
                throw new Error('未选择有效账号');
            }

            const url = `${API_CONFIG.BASE_URL}${endpoint}`;
            const headers = {
                'Content-Type': 'application/json',
                'Accept': 'application/json'
            };

            // 添加认证token（如果有）
            if (authTokens[account.id]) {
                headers['Authorization'] = `Bearer ${authTokens[account.id]}`;
            }

            const requestConfig = {
                method: method,
                headers: headers
            };

            if (method !== 'GET') {
                requestConfig.body = JSON.stringify(data);
            }

            const startTime = Date.now();
            let response, responseData, error;

            try {
                console.log(`🌐 API调用: ${method} ${url}`, data);

                response = await fetch(url, requestConfig);
                responseData = await response.json();

                if (!response.ok) {
                    throw new Error(`HTTP ${response.status}: ${responseData.message || '未知错误'}`);
                }

                console.log(`✅ API调用成功: ${endpoint}`, responseData);

            } catch (err) {
                error = err;
                console.error(`❌ API调用失败: ${endpoint}`, err);
                throw err;
            } finally {
                // 记录API调用历史
                const callRecord = {
                    timestamp: new Date().toISOString(),
                    account: account.email,
                    endpoint: endpoint,
                    method: method,
                    requestData: data,
                    responseData: responseData,
                    error: error ? error.message : null,
                    duration: Date.now() - startTime,
                    status: error ? 'error' : 'success'
                };

                apiCallHistory.unshift(callRecord);

                // 限制历史记录数量
                if (apiCallHistory.length > 100) {
                    apiCallHistory = apiCallHistory.slice(0, 100);
                }
            }

            return responseData;
        }

        // ========== 认证函数 ==========

        /**
         * @function authenticateAccount - 认证账号
         * @param {string} accountId - 账号ID
         * @returns {Promise<Object>} 认证结果
         * @description 对指定账号进行认证并获取token，包含详细的响应分析和兼容性处理
         */
        async function authenticateAccount(accountId) {
            const account = TEST_ACCOUNTS.find(acc => acc.id === accountId);
            if (!account) {
                throw new Error(`账号 ${accountId} 不存在`);
            }

            try {
                const loginData = {
                    email: account.email,
                    password: account.password
                };

                console.log(`🔐 开始认证账号: ${account.email}`);
                console.log(`📤 登录请求数据:`, loginData);
                console.log(`🌐 API端点: ${API_CONFIG.BASE_URL}${API_CONFIG.ENDPOINTS.login}`);

                const response = await makeAPICall(API_CONFIG.ENDPOINTS.login, loginData, 'POST', accountId);

                console.log(`📥 完整API响应:`, response);
                console.log(`🔍 响应类型:`, typeof response);
                console.log(`📋 响应字段:`, Object.keys(response || {}));

                // 尝试多种可能的token字段名称
                const possibleTokenFields = [
                    'access_token',
                    'token',
                    'accessToken',
                    'authToken',
                    'bearer_token',
                    'jwt',
                    'api_token'
                ];

                let foundToken = null;
                let tokenField = null;

                for (const field of possibleTokenFields) {
                    if (response && response[field]) {
                        foundToken = response[field];
                        tokenField = field;
                        console.log(`✅ 找到token字段: ${field} = ${foundToken.substring(0, 20)}...`);
                        break;
                    }
                }

                // 如果没有找到标准token字段，检查嵌套对象
                if (!foundToken && response) {
                    // 检查data字段
                    if (response.data && typeof response.data === 'object') {
                        console.log(`🔍 检查data字段:`, response.data);
                        for (const field of possibleTokenFields) {
                            if (response.data[field]) {
                                foundToken = response.data[field];
                                tokenField = `data.${field}`;
                                console.log(`✅ 在data中找到token: ${tokenField} = ${foundToken.substring(0, 20)}...`);
                                break;
                            }
                        }
                    }

                    // 检查user字段
                    if (!foundToken && response.user && typeof response.user === 'object') {
                        console.log(`🔍 检查user字段:`, response.user);
                        for (const field of possibleTokenFields) {
                            if (response.user[field]) {
                                foundToken = response.user[field];
                                tokenField = `user.${field}`;
                                console.log(`✅ 在user中找到token: ${tokenField} = ${foundToken.substring(0, 20)}...`);
                                break;
                            }
                        }
                    }
                }

                if (foundToken) {
                    authTokens[accountId] = foundToken;
                    console.log(`✅ 账号 ${account.email} 认证成功，token字段: ${tokenField}`);

                    // 更新界面状态
                    renderAccountSelector();

                    // 尝试加载动态数据
                    await loadDynamicData(accountId);

                    return {
                        success: true,
                        token: foundToken,
                        tokenField: tokenField,
                        account: account,
                        fullResponse: response
                    };
                } else {
                    // 生成详细的错误信息
                    const errorDetails = {
                        message: '认证响应中未找到有效的token字段',
                        responseKeys: Object.keys(response || {}),
                        checkedFields: possibleTokenFields,
                        fullResponse: response
                    };

                    console.error(`❌ Token查找失败:`, errorDetails);
                    throw new Error(`认证响应中未找到有效的token字段。检查的字段: ${possibleTokenFields.join(', ')}。响应字段: ${Object.keys(response || {}).join(', ')}`);
                }

            } catch (error) {
                console.error(`❌ 账号 ${account.email} 认证失败:`, error);

                // 如果是网络错误，提供更详细的信息
                if (error.name === 'TypeError' && error.message.includes('fetch')) {
                    throw new Error(`网络连接失败: ${error.message}。请检查网络连接和API端点是否正确。`);
                }

                // 如果是HTTP错误，提供状态码信息
                if (error.message.includes('HTTP')) {
                    throw new Error(`API请求失败: ${error.message}。请检查账号密码是否正确。`);
                }

                throw error;
            }
        }

        /**
         * @function loadDynamicData - 加载动态数据
         * @param {string} accountId - 账号ID
         * @description 从API加载动态数据，失败时保持静态数据
         */
        async function loadDynamicData(accountId) {
            const endpoints = ['backend_users', 'car_types', 'sub_categories', 'languages', 'regions'];

            for (const endpoint of endpoints) {
                try {
                    const response = await makeAPICall(API_CONFIG.ENDPOINTS[endpoint], {}, 'GET', accountId);

                    if (response && Array.isArray(response)) {
                        apiData[endpoint] = response;
                        dataSource[endpoint] = 'api';
                        console.log(`✅ 动态加载 ${endpoint}: ${response.length} 条记录`);
                    }
                } catch (error) {
                    console.warn(`⚠️ 动态加载 ${endpoint} 失败，保持静态数据:`, error);
                    // 保持静态数据，不改变dataSource状态
                }
            }

            // 更新状态显示
            updateDataSourceStatus();
            updateBackendUserInfo();
        }

        // ========== 测试功能函数 ==========

        /**
         * @function testAuthentication - 测试认证功能
         * @description 测试当前账号的认证功能
         */
        async function testAuthentication() {
            const resultDiv = document.getElementById('coreTestResults');
            resultDiv.style.display = 'block';
            resultDiv.className = 'test-result result-pending';
            resultDiv.innerHTML = '🔄 正在测试认证功能...';

            if (!currentAccount) {
                resultDiv.className = 'test-result result-error';
                resultDiv.innerHTML = '❌ 请先选择一个测试账号';
                return;
            }

            try {
                const result = await authenticateAccount(currentAccount.id);

                resultDiv.className = 'test-result result-success';
                resultDiv.innerHTML = `
                    <h4>✅ 认证测试成功</h4>
                    <p><strong>账号:</strong> ${result.account.email}</p>
                    <p><strong>Token:</strong> ${result.token.substring(0, 20)}...</p>
                    <p><strong>Token字段:</strong> ${result.tokenField || 'access_token'}</p>
                    <p><strong>后台用户ID:</strong> ${result.account.backendUserId || '自动选择'}</p>
                    <details>
                        <summary>查看完整响应</summary>
                        <pre>${JSON.stringify(result.fullResponse, null, 2)}</pre>
                    </details>
                `;

            } catch (error) {
                resultDiv.className = 'test-result result-error';
                resultDiv.innerHTML = `
                    <h4>❌ 认证测试失败</h4>
                    <p><strong>错误信息:</strong> ${error.message}</p>
                    <p><strong>账号:</strong> ${currentAccount.email}</p>
                    <p><strong>建议:</strong> 点击"🔐 认证问题诊断"按钮获取详细分析</p>
                `;
            }
        }

        /**
         * @function testDataLoading - 测试数据加载功能
         * @description 测试API数据加载和降级机制
         */
        async function testDataLoading() {
            const resultDiv = document.getElementById('coreTestResults');
            resultDiv.style.display = 'block';
            resultDiv.className = 'test-result result-pending';
            resultDiv.innerHTML = '🔄 正在测试数据加载功能...';

            if (!currentAccount) {
                resultDiv.className = 'test-result result-error';
                resultDiv.innerHTML = '❌ 请先选择一个测试账号';
                return;
            }

            try {
                // 确保账号已认证
                if (!authTokens[currentAccount.id]) {
                    await authenticateAccount(currentAccount.id);
                }

                // 测试数据加载
                await loadDynamicData(currentAccount.id);

                const results = Object.keys(apiData).map(key => {
                    const count = apiData[key].length;
                    const source = dataSource[key];
                    return `<li><strong>${key}:</strong> ${count} 条记录 (${source === 'api' ? '动态API' : '静态映射'})</li>`;
                }).join('');

                resultDiv.className = 'test-result result-success';
                resultDiv.innerHTML = `
                    <h4>✅ 数据加载测试完成</h4>
                    <ul>${results}</ul>
                `;

            } catch (error) {
                resultDiv.className = 'test-result result-error';
                resultDiv.innerHTML = `
                    <h4>❌ 数据加载测试失败</h4>
                    <p><strong>错误信息:</strong> ${error.message}</p>
                `;
            }
        }

        /**
         * @function testFallbackMechanism - 测试降级机制
         * @description 测试API失败时的静态数据降级机制
         */
        async function testFallbackMechanism() {
            const resultDiv = document.getElementById('coreTestResults');
            resultDiv.style.display = 'block';
            resultDiv.className = 'test-result result-pending';
            resultDiv.innerHTML = '🔄 正在测试降级机制...';

            try {
                // 清除认证token以模拟API失败
                const originalToken = authTokens[currentAccount?.id];
                if (currentAccount) {
                    delete authTokens[currentAccount.id];
                }

                // 重新加载静态数据
                loadStaticData();
                updateDataSourceStatus();

                const results = Object.keys(STATIC_DATA).map(key => {
                    const count = STATIC_DATA[key].length;
                    return `<li><strong>${key}:</strong> ${count} 条静态记录</li>`;
                }).join('');

                resultDiv.className = 'test-result result-success';
                resultDiv.innerHTML = `
                    <h4>✅ 降级机制测试成功</h4>
                    <p>已成功切换到静态数据源:</p>
                    <ul>${results}</ul>
                `;

                // 恢复原始token
                if (currentAccount && originalToken) {
                    authTokens[currentAccount.id] = originalToken;
                }

            } catch (error) {
                resultDiv.className = 'test-result result-error';
                resultDiv.innerHTML = `
                    <h4>❌ 降级机制测试失败</h4>
                    <p><strong>错误信息:</strong> ${error.message}</p>
                `;
            }
        }

        /**
         * @function testLanguagesArrayFormat - 测试语言数组格式
         * @description 测试languages_id_array的对象格式和数组格式兼容性
         */
        async function testLanguagesArrayFormat() {
            const resultDiv = document.getElementById('coreTestResults');
            resultDiv.style.display = 'block';
            resultDiv.className = 'test-result result-pending';
            resultDiv.innerHTML = '🔄 正在测试语言数组格式...';

            try {
                const testFormats = [
                    {
                        name: '对象格式 (推荐)',
                        data: { "0": "2", "1": "4" }, // English + Chinese
                        expected: 'success'
                    },
                    {
                        name: '数组格式',
                        data: [2, 4], // English + Chinese
                        expected: 'may_fail'
                    }
                ];

                let results = [];

                for (const format of testFormats) {
                    try {
                        const testOrder = {
                            pickup_location: 'KLIA Airport',
                            destination: 'KLCC',
                            pickup_date: '25-12-2024',
                            pickup_time: '10:00',
                            car_type_id: 5,
                            sub_category_id: 2,
                            languages_id_array: format.data,
                            incharge_by_backend_user_id: currentAccount?.backendUserId || 1
                        };

                        // 这里只是格式验证，不实际调用API
                        const isValidFormat = typeof format.data === 'object';

                        results.push(`
                            <li>
                                <strong>${format.name}:</strong>
                                ${isValidFormat ? '✅ 格式有效' : '⚠️ 格式可能有问题'}
                                <br>
                                <code>${JSON.stringify(format.data)}</code>
                            </li>
                        `);

                    } catch (error) {
                        results.push(`
                            <li>
                                <strong>${format.name}:</strong>
                                ❌ 测试失败 - ${error.message}
                            </li>
                        `);
                    }
                }

                resultDiv.className = 'test-result result-success';
                resultDiv.innerHTML = `
                    <h4>🗣️ 语言格式测试完成</h4>
                    <p><strong>推荐使用对象格式:</strong> {"0":"1","1":"2"}</p>
                    <ul>${results.join('')}</ul>
                    <p><em>注意: 对象格式具有更好的API兼容性，可避免HTTP 500错误</em></p>
                `;

            } catch (error) {
                resultDiv.className = 'test-result result-error';
                resultDiv.innerHTML = `
                    <h4>❌ 语言格式测试失败</h4>
                    <p><strong>错误信息:</strong> ${error.message}</p>
                `;
            }
        }

        /**
         * @function runMultiAccountTest - 运行多账号测试
         * @description 对所有账号进行并行认证测试
         */
        async function runMultiAccountTest() {
            const resultDiv = document.getElementById('multiAccountResults');
            resultDiv.style.display = 'block';
            resultDiv.className = 'test-result result-pending';
            resultDiv.innerHTML = '🔄 正在运行多账号测试...';

            try {
                const testPromises = TEST_ACCOUNTS.map(async (account) => {
                    try {
                        const result = await authenticateAccount(account.id);
                        return {
                            account: account,
                            success: true,
                            token: result.token,
                            error: null
                        };
                    } catch (error) {
                        return {
                            account: account,
                            success: false,
                            token: null,
                            error: error.message
                        };
                    }
                });

                const results = await Promise.all(testPromises);

                const successCount = results.filter(r => r.success).length;
                const failCount = results.filter(r => !r.success).length;

                const resultRows = results.map(result => `
                    <tr>
                        <td>${result.account.name}</td>
                        <td>${result.account.email}</td>
                        <td>${result.success ? '✅ 成功' : '❌ 失败'}</td>
                        <td>${result.success ? result.token.substring(0, 20) + '...' : result.error}</td>
                        <td>${result.account.backendUserId || '自动选择'}</td>
                    </tr>
                `).join('');

                testResults.multiAccount = results;

                resultDiv.className = 'test-result result-success';
                resultDiv.innerHTML = `
                    <h4>👥 多账号测试完成</h4>
                    <p><strong>成功:</strong> ${successCount} 个账号 | <strong>失败:</strong> ${failCount} 个账号</p>
                    <table class="comparison-table">
                        <thead>
                            <tr>
                                <th>账号名称</th>
                                <th>邮箱</th>
                                <th>认证状态</th>
                                <th>Token/错误信息</th>
                                <th>后台用户ID</th>
                            </tr>
                        </thead>
                        <tbody>
                            ${resultRows}
                        </tbody>
                    </table>
                `;

            } catch (error) {
                resultDiv.className = 'test-result result-error';
                resultDiv.innerHTML = `
                    <h4>❌ 多账号测试失败</h4>
                    <p><strong>错误信息:</strong> ${error.message}</p>
                `;
            }
        }

        /**
         * @function createManualTestOrder - 创建手动测试订单
         * @description 根据用户输入创建测试订单，支持完整字段覆盖
         */
        async function createManualTestOrder() {
            const resultDiv = document.getElementById('manualTestResults');
            resultDiv.style.display = 'block';
            resultDiv.className = 'test-result result-pending';
            resultDiv.innerHTML = '🔄 正在创建测试订单...';

            if (!currentAccount) {
                resultDiv.className = 'test-result result-error';
                resultDiv.innerHTML = '❌ 请先选择一个测试账号';
                return;
            }

            try {
                // 确保账号已认证
                if (!authTokens[currentAccount.id]) {
                    await authenticateAccount(currentAccount.id);
                }

                // 获取表单数据
                const orderType = document.getElementById('testOrderType').value;
                const passengerCount = parseInt(document.getElementById('testPassengerCount').value);
                const pickup = document.getElementById('testPickup').value;
                const destination = document.getElementById('testDestination').value;
                const customerName = document.getElementById('testCustomerName').value;
                const customerPhone = document.getElementById('testCustomerPhone').value;
                const customerEmail = document.getElementById('testCustomerEmail').value;
                const pickupDate = document.getElementById('testPickupDate').value;
                const pickupTime = document.getElementById('testPickupTime').value;

                // 验证必填字段
                if (!pickup || !destination || !customerName || !customerPhone || !pickupDate || !pickupTime) {
                    resultDiv.className = 'test-result result-error';
                    resultDiv.innerHTML = '❌ 请填写所有必填字段';
                    return;
                }

                // 验证日期格式 (DD-MM-YYYY)
                const datePattern = /^\d{2}-\d{2}-\d{4}$/;
                if (!datePattern.test(pickupDate)) {
                    resultDiv.className = 'test-result result-error';
                    resultDiv.innerHTML = '❌ 日期格式错误，请使用DD-MM-YYYY格式（如：25-12-2024）';
                    return;
                }

                // 智能选择子分类
                const subCategory = apiData.subCategories.find(sc =>
                    sc.name.toLowerCase().includes(orderType) ||
                    (sc.id === 2 && orderType === 'pickup') ||
                    (sc.id === 3 && orderType === 'dropoff') ||
                    (sc.id === 4 && orderType === 'charter')
                ) || apiData.subCategories[0];

                // 智能选择车型（根据人数）
                const carType = apiData.carTypes.find(ct => {
                    const seatNumber = parseInt(ct.type.match(/(\d+)/)?.[1] || '0');
                    return seatNumber >= passengerCount;
                }) || apiData.carTypes.find(ct => ct.id === 5); // 默认5座

                // 获取后台用户ID
                const backendUserId = currentAccount.backendUserId ||
                                    (apiData.backendUsers[0] ? apiData.backendUsers[0].id : 1);

                // 构建完整的订单数据
                const orderData = {
                    pickup_location: pickup,
                    destination: destination,
                    pickup_date: pickupDate,
                    pickup_time: pickupTime,
                    passenger_count: passengerCount,
                    car_type_id: carType?.id || 5,
                    sub_category_id: subCategory?.id || 2,
                    languages_id_array: {"0": "2", "1": "4"}, // 对象格式：英语+中文
                    incharge_by_backend_user_id: backendUserId,
                    customer_name: customerName,
                    customer_phone: customerPhone,
                    customer_email: customerEmail,
                    remarks: `手动测试订单 - ${new Date().toLocaleString()}`,
                    // 可选字段
                    flight_number: '',
                    special_requirements: ''
                };

                const response = await makeAPICall(API_CONFIG.ENDPOINTS.create_order, orderData);

                resultDiv.className = 'test-result result-success';
                resultDiv.innerHTML = `
                    <h4>✅ 测试订单创建成功</h4>
                    <div class="grid-2" style="margin: 15px 0;">
                        <div>
                            <p><strong>订单ID:</strong> ${response.order_id || response.id || '未返回'}</p>
                            <p><strong>订单类型:</strong> ${orderType}</p>
                            <p><strong>子分类:</strong> ${subCategory?.name || '未知'} (ID: ${subCategory?.id || 'N/A'})</p>
                            <p><strong>车型:</strong> ${carType?.type || '未知'} (ID: ${carType?.id || 'N/A'})</p>
                        </div>
                        <div>
                            <p><strong>客户姓名:</strong> ${customerName}</p>
                            <p><strong>客户电话:</strong> ${customerPhone}</p>
                            <p><strong>乘客人数:</strong> ${passengerCount}</p>
                            <p><strong>后台用户:</strong> ID ${backendUserId}</p>
                        </div>
                    </div>
                    <p><strong>服务详情:</strong> ${pickup} → ${destination}</p>
                    <p><strong>日期时间:</strong> ${pickupDate} ${pickupTime}</p>
                    <details>
                        <summary>查看完整API请求数据</summary>
                        <pre>${JSON.stringify(orderData, null, 2)}</pre>
                    </details>
                    <details>
                        <summary>查看完整API响应</summary>
                        <pre>${JSON.stringify(response, null, 2)}</pre>
                    </details>
                `;

            } catch (error) {
                resultDiv.className = 'test-result result-error';
                resultDiv.innerHTML = `
                    <h4>❌ 测试订单创建失败</h4>
                    <p><strong>错误信息:</strong> ${error.message}</p>
                    <p><strong>账号:</strong> ${currentAccount.email}</p>
                    <p><strong>建议:</strong></p>
                    <ul>
                        <li>检查所有必填字段是否已填写</li>
                        <li>确认日期格式为DD-MM-YYYY</li>
                        <li>验证电话号码格式</li>
                        <li>点击"🚨 500错误专项诊断"获取详细分析</li>
                    </ul>
                `;
            }
        }

        /**
         * @function previewOrderData - 预览订单数据
         * @description 预览将要发送的订单数据，不实际创建订单，支持完整字段预览
         */
        function previewOrderData() {
            const resultDiv = document.getElementById('manualTestResults');
            resultDiv.style.display = 'block';

            if (!currentAccount) {
                resultDiv.className = 'test-result result-error';
                resultDiv.innerHTML = '❌ 请先选择一个测试账号';
                return;
            }

            try {
                // 获取表单数据
                const orderType = document.getElementById('testOrderType').value;
                const passengerCount = parseInt(document.getElementById('testPassengerCount').value);
                const pickup = document.getElementById('testPickup').value;
                const destination = document.getElementById('testDestination').value;
                const customerName = document.getElementById('testCustomerName').value;
                const customerPhone = document.getElementById('testCustomerPhone').value;
                const customerEmail = document.getElementById('testCustomerEmail').value;
                const pickupDate = document.getElementById('testPickupDate').value;
                const pickupTime = document.getElementById('testPickupTime').value;

                // 智能选择逻辑
                const subCategory = apiData.subCategories.find(sc =>
                    sc.name.toLowerCase().includes(orderType) ||
                    (sc.id === 2 && orderType === 'pickup') ||
                    (sc.id === 3 && orderType === 'dropoff') ||
                    (sc.id === 4 && orderType === 'charter')
                ) || apiData.subCategories[0];

                const carType = apiData.carTypes.find(ct => {
                    const seatNumber = parseInt(ct.type.match(/(\d+)/)?.[1] || '0');
                    return seatNumber >= passengerCount;
                }) || apiData.carTypes.find(ct => ct.id === 5);

                const backendUserId = currentAccount.backendUserId ||
                                    (apiData.backendUsers[0] ? apiData.backendUsers[0].id : 1);

                // 构建完整的订单数据
                const orderData = {
                    pickup_location: pickup,
                    destination: destination,
                    pickup_date: pickupDate,
                    pickup_time: pickupTime,
                    passenger_count: passengerCount,
                    car_type_id: carType?.id || 5,
                    sub_category_id: subCategory?.id || 2,
                    languages_id_array: {"0": "2", "1": "4"}, // 对象格式：英语+中文
                    incharge_by_backend_user_id: backendUserId,
                    customer_name: customerName,
                    customer_phone: customerPhone,
                    customer_email: customerEmail,
                    remarks: `预览订单数据 - ${new Date().toLocaleString()}`,
                    flight_number: '',
                    special_requirements: ''
                };

                // 验证字段完整性
                const missingFields = [];
                if (!pickup) missingFields.push('接机地点');
                if (!destination) missingFields.push('目的地');
                if (!customerName) missingFields.push('客户姓名');
                if (!customerPhone) missingFields.push('客户电话');
                if (!customerEmail) missingFields.push('客户邮箱');
                if (!pickupDate) missingFields.push('接机日期');
                if (!pickupTime) missingFields.push('接机时间');

                // 验证日期格式
                const datePattern = /^\d{2}-\d{2}-\d{4}$/;
                const dateValid = datePattern.test(pickupDate);

                resultDiv.className = 'test-result result-warning';
                resultDiv.innerHTML = `
                    <h4>👁️ 订单数据预览</h4>

                    ${missingFields.length > 0 ? `
                        <div style="background: #fff3cd; border: 1px solid #ffeaa7; border-radius: 4px; padding: 10px; margin: 10px 0;">
                            <strong>⚠️ 缺少必填字段:</strong> ${missingFields.join(', ')}
                        </div>
                    ` : ''}

                    ${!dateValid ? `
                        <div style="background: #f8d7da; border: 1px solid #f5c6cb; border-radius: 4px; padding: 10px; margin: 10px 0;">
                            <strong>❌ 日期格式错误:</strong> 请使用DD-MM-YYYY格式（如：25-12-2024）
                        </div>
                    ` : ''}

                    <div class="grid-2" style="margin: 15px 0;">
                        <div>
                            <h5>📋 基础信息</h5>
                            <ul>
                                <li><strong>订单类型:</strong> ${orderType}</li>
                                <li><strong>乘客人数:</strong> ${passengerCount}</li>
                                <li><strong>日期时间:</strong> ${pickupDate} ${pickupTime}</li>
                                <li><strong>服务路线:</strong> ${pickup} → ${destination}</li>
                            </ul>
                        </div>
                        <div>
                            <h5>👤 客户信息</h5>
                            <ul>
                                <li><strong>姓名:</strong> ${customerName || '未填写'}</li>
                                <li><strong>电话:</strong> ${customerPhone || '未填写'}</li>
                                <li><strong>邮箱:</strong> ${customerEmail || '未填写'}</li>
                            </ul>
                        </div>
                    </div>

                    <h5>🤖 智能选择结果</h5>
                    <ul>
                        <li><strong>子分类:</strong> ${subCategory?.name || '未知'} (ID: ${subCategory?.id || 'N/A'})</li>
                        <li><strong>车型:</strong> ${carType?.type || '未知'} (ID: ${carType?.id || 'N/A'})</li>
                        <li><strong>后台用户:</strong> ID ${backendUserId}</li>
                        <li><strong>语言格式:</strong> 对象格式 {"0":"2","1":"4"} (推荐)</li>
                    </ul>

                    <details>
                        <summary>查看完整API请求数据</summary>
                        <pre>${JSON.stringify(orderData, null, 2)}</pre>
                    </details>

                    <div style="margin-top: 15px;">
                        ${missingFields.length === 0 && dateValid ?
                            '<p style="color: #28a745;"><strong>✅ 数据验证通过，可以创建订单</strong></p>' :
                            '<p style="color: #dc3545;"><strong>❌ 请修正上述问题后再创建订单</strong></p>'
                        }
                        <p><em>这只是预览，未实际创建订单。点击"🚗 创建测试订单"按钮执行实际创建。</em></p>
                    </div>
                `;

            } catch (error) {
                resultDiv.className = 'test-result result-error';
                resultDiv.innerHTML = `
                    <h4>❌ 订单数据预览失败</h4>
                    <p><strong>错误信息:</strong> ${error.message}</p>
                `;
            }
        }

        // ========== 响应分析和验证函数 ==========

        /**
         * @function analyzeOrderResponse - 分析订单创建响应
         * @param {Object} response - API响应对象
         * @returns {Object} 分析结果包含订单ID和响应结构信息
         * @description 智能分析订单创建响应，检测多种可能的订单ID字段
         */
        function analyzeOrderResponse(response) {
            const analysis = {
                orderId: null,
                orderIdField: null,
                responseStructure: {},
                possibleIds: [],
                warnings: []
            };

            // 记录响应结构
            analysis.responseStructure = {
                topLevelKeys: Object.keys(response || {}),
                responseType: typeof response,
                hasData: !!response?.data,
                hasNestedObjects: false
            };

            // 可能的订单ID字段名称
            const possibleOrderIdFields = [
                'order_id',
                'id',
                'orderId',
                'order_number',
                'orderNumber',
                'reference_id',
                'referenceId',
                'booking_id',
                'bookingId',
                'transaction_id',
                'transactionId'
            ];

            // 检查顶级字段
            for (const field of possibleOrderIdFields) {
                if (response && response[field]) {
                    analysis.possibleIds.push({
                        field: field,
                        value: response[field],
                        location: 'root',
                        type: typeof response[field]
                    });

                    if (!analysis.orderId) {
                        analysis.orderId = response[field];
                        analysis.orderIdField = field;
                    }
                }
            }

            // 检查data嵌套对象
            if (response?.data && typeof response.data === 'object') {
                analysis.responseStructure.hasNestedObjects = true;
                analysis.responseStructure.dataKeys = Object.keys(response.data);

                for (const field of possibleOrderIdFields) {
                    if (response.data[field]) {
                        analysis.possibleIds.push({
                            field: `data.${field}`,
                            value: response.data[field],
                            location: 'data',
                            type: typeof response.data[field]
                        });

                        if (!analysis.orderId) {
                            analysis.orderId = response.data[field];
                            analysis.orderIdField = `data.${field}`;
                        }
                    }
                }
            }

            // 检查其他可能的嵌套对象
            const nestedObjects = ['result', 'order', 'booking', 'response'];
            for (const objKey of nestedObjects) {
                if (response?.[objKey] && typeof response[objKey] === 'object') {
                    analysis.responseStructure.hasNestedObjects = true;

                    for (const field of possibleOrderIdFields) {
                        if (response[objKey][field]) {
                            analysis.possibleIds.push({
                                field: `${objKey}.${field}`,
                                value: response[objKey][field],
                                location: objKey,
                                type: typeof response[objKey][field]
                            });

                            if (!analysis.orderId) {
                                analysis.orderId = response[objKey][field];
                                analysis.orderIdField = `${objKey}.${field}`;
                            }
                        }
                    }
                }
            }

            // 生成警告信息
            if (!analysis.orderId) {
                analysis.warnings.push('未找到标准的订单ID字段');
            }

            if (analysis.possibleIds.length === 0) {
                analysis.warnings.push('响应中没有发现任何可能的ID字段');
            }

            if (analysis.possibleIds.length > 1) {
                analysis.warnings.push(`发现多个可能的ID字段: ${analysis.possibleIds.map(p => p.field).join(', ')}`);
            }

            return analysis;
        }

        /**
         * @function validateEndpointHealth - 验证端点健康状态
         * @param {string} accountId - 账号ID
         * @returns {Promise<Object>} 端点健康状态报告
         * @description 检查所有API端点的可用性和响应状态
         */
        async function validateEndpointHealth(accountId = null) {
            const account = accountId ? TEST_ACCOUNTS.find(acc => acc.id === accountId) : currentAccount;
            if (!account) {
                throw new Error('未选择有效账号');
            }

            const endpoints = [
                { name: 'backend_users', url: API_CONFIG.ENDPOINTS.backend_users, critical: true },
                { name: 'car_types', url: API_CONFIG.ENDPOINTS.car_types, critical: true },
                { name: 'sub_categories', url: API_CONFIG.ENDPOINTS.sub_categories, critical: true },
                { name: 'languages', url: API_CONFIG.ENDPOINTS.languages, critical: false },
                { name: 'regions', url: API_CONFIG.ENDPOINTS.regions, critical: false }
            ];

            const healthReport = {
                timestamp: new Date().toISOString(),
                account: account.email,
                overallHealth: 'unknown',
                workingEndpoints: [],
                failingEndpoints: [],
                criticalIssues: [],
                warnings: [],
                suggestions: []
            };

            for (const endpoint of endpoints) {
                try {
                    const startTime = Date.now();
                    const response = await makeAPICall(endpoint.url, {}, 'GET', account.id);
                    const responseTime = Date.now() - startTime;

                    const endpointStatus = {
                        name: endpoint.name,
                        url: endpoint.url,
                        status: 'working',
                        responseTime: responseTime,
                        dataCount: Array.isArray(response) ? response.length : 0,
                        critical: endpoint.critical
                    };

                    healthReport.workingEndpoints.push(endpointStatus);

                } catch (error) {
                    const endpointStatus = {
                        name: endpoint.name,
                        url: endpoint.url,
                        status: 'failed',
                        error: error.message,
                        critical: endpoint.critical
                    };

                    healthReport.failingEndpoints.push(endpointStatus);

                    if (endpoint.critical) {
                        healthReport.criticalIssues.push(`关键端点 ${endpoint.name} 失败: ${error.message}`);
                    } else {
                        healthReport.warnings.push(`端点 ${endpoint.name} 失败: ${error.message}`);
                    }
                }
            }

            // 计算整体健康状态
            const totalEndpoints = endpoints.length;
            const workingCount = healthReport.workingEndpoints.length;
            const criticalFailures = healthReport.criticalIssues.length;

            if (criticalFailures > 0) {
                healthReport.overallHealth = 'critical';
            } else if (workingCount === totalEndpoints) {
                healthReport.overallHealth = 'healthy';
            } else if (workingCount >= totalEndpoints * 0.7) {
                healthReport.overallHealth = 'warning';
            } else {
                healthReport.overallHealth = 'unhealthy';
            }

            // 生成建议
            if (healthReport.failingEndpoints.length > 0) {
                healthReport.suggestions.push('检查失败端点的URL是否正确');
                healthReport.suggestions.push('验证API认证token是否有效');
                healthReport.suggestions.push('确认API版本兼容性');
            }

            if (criticalFailures > 0) {
                healthReport.suggestions.push('关键端点失败可能影响订单创建，建议使用静态降级数据');
            }

            return healthReport;
        }

        /**
         * @function generateDetailedTestReport - 生成详细测试报告
         * @param {Array} orders - 订单测试结果数组
         * @param {Object} healthReport - 端点健康报告
         * @returns {Object} 详细的测试分析报告
         * @description 生成包含响应分析和改进建议的详细测试报告
         */
        function generateDetailedTestReport(orders, healthReport = null) {
            const report = {
                summary: {
                    totalOrders: orders.length,
                    successCount: orders.filter(o => o.status === 'success').length,
                    failCount: orders.filter(o => o.status === 'failed').length,
                    successRate: 0,
                    avgResponseTime: 0
                },
                responseAnalysis: {
                    orderIdIssues: [],
                    responseStructures: [],
                    commonPatterns: {},
                    recommendations: []
                },
                endpointHealth: healthReport,
                improvements: []
            };

            // 计算基础统计
            report.summary.successRate = (report.summary.successCount / report.summary.totalOrders * 100).toFixed(1);

            // 分析响应结构
            const successfulOrders = orders.filter(o => o.status === 'success');

            for (const order of successfulOrders) {
                if (order.response) {
                    const analysis = analyzeOrderResponse(order.response);

                    if (!analysis.orderId) {
                        report.responseAnalysis.orderIdIssues.push({
                            orderIndex: order.index,
                            issue: '未找到订单ID',
                            responseKeys: analysis.responseStructure.topLevelKeys,
                            suggestions: analysis.warnings
                        });
                    }

                    report.responseAnalysis.responseStructures.push({
                        orderIndex: order.index,
                        structure: analysis.responseStructure,
                        possibleIds: analysis.possibleIds
                    });
                }
            }

            // 生成改进建议
            if (report.responseAnalysis.orderIdIssues.length > 0) {
                report.improvements.push({
                    priority: 'high',
                    category: 'response_validation',
                    issue: '订单ID缺失',
                    description: `${report.responseAnalysis.orderIdIssues.length}个订单未返回有效的订单ID`,
                    recommendation: '实现智能订单ID检测，检查多种可能的字段名称和嵌套结构'
                });
            }

            if (healthReport && healthReport.criticalIssues.length > 0) {
                report.improvements.push({
                    priority: 'high',
                    category: 'endpoint_health',
                    issue: '关键端点失败',
                    description: healthReport.criticalIssues.join('; '),
                    recommendation: '修复端点URL或实现更强大的降级机制'
                });
            }

            if (healthReport && healthReport.failingEndpoints.length > 0) {
                report.improvements.push({
                    priority: 'medium',
                    category: 'endpoint_reliability',
                    issue: '部分端点不可用',
                    description: `${healthReport.failingEndpoints.length}个端点返回404或其他错误`,
                    recommendation: '验证端点URL，检查API版本兼容性，实现端点自动发现'
                });
            }

            return report;
        }

        // ========== 工具函数 ==========

        /**
         * @function showApiHistory - 显示API调用历史
         * @description 显示最近的API调用记录
         */
        function showApiHistory() {
            const resultDiv = document.getElementById('apiHistoryResults');
            resultDiv.style.display = 'block';

            if (apiCallHistory.length === 0) {
                resultDiv.className = 'test-result result-warning';
                resultDiv.innerHTML = '📋 暂无API调用历史记录';
                return;
            }

            const historyRows = apiCallHistory.slice(0, 20).map(call => `
                <tr>
                    <td>${new Date(call.timestamp).toLocaleString()}</td>
                    <td>${call.account}</td>
                    <td>${call.method} ${call.endpoint}</td>
                    <td>${call.status === 'success' ? '✅' : '❌'}</td>
                    <td>${call.duration}ms</td>
                    <td>${call.error || '成功'}</td>
                </tr>
            `).join('');

            resultDiv.className = 'test-result result-success';
            resultDiv.innerHTML = `
                <h4>📜 API调用历史 (最近20条)</h4>
                <table class="comparison-table">
                    <thead>
                        <tr>
                            <th>时间</th>
                            <th>账号</th>
                            <th>端点</th>
                            <th>状态</th>
                            <th>耗时</th>
                            <th>错误信息</th>
                        </tr>
                    </thead>
                    <tbody>
                        ${historyRows}
                    </tbody>
                </table>
            `;
        }

        /**
         * @function exportApiLogs - 导出API日志
         * @description 将API调用历史导出为JSON文件
         */
        function exportApiLogs() {
            const resultDiv = document.getElementById('apiHistoryResults');
            resultDiv.style.display = 'block';

            try {
                const exportData = {
                    exportTime: new Date().toISOString(),
                    totalCalls: apiCallHistory.length,
                    calls: apiCallHistory
                };

                const dataStr = JSON.stringify(exportData, null, 2);
                const dataBlob = new Blob([dataStr], { type: 'application/json' });
                const url = URL.createObjectURL(dataBlob);

                const link = document.createElement('a');
                link.href = url;
                link.download = `gomyhire-api-logs-${new Date().toISOString().split('T')[0]}.json`;
                link.click();

                URL.revokeObjectURL(url);

                resultDiv.className = 'test-result result-success';
                resultDiv.innerHTML = `
                    <h4>💾 API日志导出成功</h4>
                    <p>已导出 ${apiCallHistory.length} 条API调用记录</p>
                `;

            } catch (error) {
                resultDiv.className = 'test-result result-error';
                resultDiv.innerHTML = `
                    <h4>❌ API日志导出失败</h4>
                    <p><strong>错误信息:</strong> ${error.message}</p>
                `;
            }
        }

        /**
         * @function clearApiHistory - 清空API历史
         * @description 清空所有API调用历史记录
         */
        function clearApiHistory() {
            const resultDiv = document.getElementById('apiHistoryResults');
            resultDiv.style.display = 'block';

            const count = apiCallHistory.length;
            apiCallHistory.length = 0; // 清空数组

            resultDiv.className = 'test-result result-success';
            resultDiv.innerHTML = `
                <h4>🗑️ API历史已清空</h4>
                <p>已清空 ${count} 条历史记录</p>
            `;
        }

        /**
         * @function diagnose500Error - 诊断500错误
         * @description 专门诊断HTTP 500错误的常见原因
         */
        function diagnose500Error() {
            const resultDiv = document.getElementById('diagnosticResults');
            resultDiv.style.display = 'block';
            resultDiv.className = 'test-result result-warning';

            const diagnosticChecks = [
                {
                    name: 'languages_id_array格式检查',
                    check: () => {
                        // 检查最近的API调用中是否使用了数组格式
                        const recentCalls = apiCallHistory.slice(0, 5);
                        const hasArrayFormat = recentCalls.some(call =>
                            call.requestData &&
                            Array.isArray(call.requestData.languages_id_array)
                        );
                        return {
                            status: hasArrayFormat ? 'warning' : 'ok',
                            message: hasArrayFormat ?
                                '发现使用数组格式，建议改用对象格式 {"0":"1","1":"2"}' :
                                '语言格式正常'
                        };
                    }
                },
                {
                    name: '认证状态检查',
                    check: () => {
                        const hasToken = currentAccount && authTokens[currentAccount.id];
                        return {
                            status: hasToken ? 'ok' : 'error',
                            message: hasToken ? '认证token有效' : '缺少有效的认证token'
                        };
                    }
                },
                {
                    name: '必填字段检查',
                    check: () => {
                        const requiredFields = [
                            'pickup_location', 'destination', 'pickup_date',
                            'pickup_time', 'car_type_id', 'sub_category_id'
                        ];
                        return {
                            status: 'info',
                            message: `确保包含所有必填字段: ${requiredFields.join(', ')}`
                        };
                    }
                },
                {
                    name: '日期格式检查',
                    check: () => {
                        return {
                            status: 'info',
                            message: '确保使用DD-MM-YYYY日期格式，如: 25-12-2024'
                        };
                    }
                }
            ];

            const checkResults = diagnosticChecks.map(check => {
                const result = check.check();
                const icon = result.status === 'ok' ? '✅' :
                           result.status === 'warning' ? '⚠️' :
                           result.status === 'error' ? '❌' : 'ℹ️';
                return `<li>${icon} <strong>${check.name}:</strong> ${result.message}</li>`;
            }).join('');

            resultDiv.innerHTML = `
                <h4>🚨 HTTP 500错误诊断</h4>
                <ul>${checkResults}</ul>
                <p><strong>常见解决方案:</strong></p>
                <ol>
                    <li>将languages_id_array改为对象格式: {"0":"2","1":"4"}</li>
                    <li>确保所有必填字段都已填写</li>
                    <li>检查日期格式是否为DD-MM-YYYY</li>
                    <li>验证car_type_id和sub_category_id是否有效</li>
                    <li>确保认证token未过期</li>
                </ol>
            `;
        }

        /**
         * @function debugLastAPICall - 调试最后的API调用
         * @description 显示最后一次API调用的详细信息
         */
        function debugLastAPICall() {
            const resultDiv = document.getElementById('diagnosticResults');
            resultDiv.style.display = 'block';

            if (apiCallHistory.length === 0) {
                resultDiv.className = 'test-result result-warning';
                resultDiv.innerHTML = '🔍 暂无API调用记录可供调试';
                return;
            }

            const lastCall = apiCallHistory[0];
            const statusClass = lastCall.status === 'success' ? 'result-success' : 'result-error';

            resultDiv.className = `test-result ${statusClass}`;
            resultDiv.innerHTML = `
                <h4>🔍 最后API调用调试信息</h4>
                <p><strong>时间:</strong> ${new Date(lastCall.timestamp).toLocaleString()}</p>
                <p><strong>账号:</strong> ${lastCall.account}</p>
                <p><strong>端点:</strong> ${lastCall.method} ${lastCall.endpoint}</p>
                <p><strong>状态:</strong> ${lastCall.status === 'success' ? '✅ 成功' : '❌ 失败'}</p>
                <p><strong>耗时:</strong> ${lastCall.duration}ms</p>
                ${lastCall.error ? `<p><strong>错误:</strong> ${lastCall.error}</p>` : ''}

                <details>
                    <summary>请求数据</summary>
                    <pre>${JSON.stringify(lastCall.requestData, null, 2)}</pre>
                </details>

                ${lastCall.responseData ? `
                    <details>
                        <summary>响应数据</summary>
                        <pre>${JSON.stringify(lastCall.responseData, null, 2)}</pre>
                    </details>
                ` : ''}
            `;
        }

        /**
         * @function testConcurrentLoading - 测试并发加载
         * @description 测试多个API端点的并发加载性能
         */
        async function testConcurrentLoading() {
            const resultDiv = document.getElementById('coreTestResults');
            resultDiv.style.display = 'block';
            resultDiv.className = 'test-result result-pending';
            resultDiv.innerHTML = '🔄 正在测试并发加载性能...';

            if (!currentAccount || !authTokens[currentAccount.id]) {
                resultDiv.className = 'test-result result-error';
                resultDiv.innerHTML = '❌ 请先认证当前账号';
                return;
            }

            try {
                const startTime = Date.now();
                const endpoints = ['backend_users', 'car_types', 'sub_categories', 'languages'];

                const promises = endpoints.map(endpoint =>
                    makeAPICall(API_CONFIG.ENDPOINTS[endpoint], {}, 'GET', currentAccount.id)
                        .then(data => ({ endpoint, success: true, data, error: null }))
                        .catch(error => ({ endpoint, success: false, data: null, error: error.message }))
                );

                const results = await Promise.all(promises);
                const totalTime = Date.now() - startTime;

                const successCount = results.filter(r => r.success).length;
                const failCount = results.filter(r => !r.success).length;

                const resultRows = results.map(result => `
                    <tr>
                        <td>${result.endpoint}</td>
                        <td>${result.success ? '✅ 成功' : '❌ 失败'}</td>
                        <td>${result.success ? (result.data?.length || 0) + ' 条记录' : result.error}</td>
                    </tr>
                `).join('');

                resultDiv.className = 'test-result result-success';
                resultDiv.innerHTML = `
                    <h4>⚡ 并发加载测试完成</h4>
                    <p><strong>总耗时:</strong> ${totalTime}ms</p>
                    <p><strong>成功:</strong> ${successCount} 个端点 | <strong>失败:</strong> ${failCount} 个端点</p>
                    <table class="comparison-table">
                        <thead>
                            <tr>
                                <th>端点</th>
                                <th>状态</th>
                                <th>结果</th>
                            </tr>
                        </thead>
                        <tbody>
                            ${resultRows}
                        </tbody>
                    </table>
                `;

            } catch (error) {
                resultDiv.className = 'test-result result-error';
                resultDiv.innerHTML = `
                    <h4>❌ 并发加载测试失败</h4>
                    <p><strong>错误信息:</strong> ${error.message}</p>
                `;
            }
        }

        /**
         * @function simulateAPIFailure - 模拟API失败
         * @description 模拟API失败情况以测试错误处理
         */
        async function simulateAPIFailure() {
            const resultDiv = document.getElementById('coreTestResults');
            resultDiv.style.display = 'block';
            resultDiv.className = 'test-result result-pending';
            resultDiv.innerHTML = '🔄 正在模拟API失败情况...';

            try {
                // 使用无效的端点来模拟失败
                const invalidEndpoint = '/invalid_endpoint_test';

                try {
                    await makeAPICall(invalidEndpoint, {}, 'GET');
                    // 如果没有抛出错误，说明模拟失败
                    throw new Error('模拟失败：API调用意外成功');
                } catch (apiError) {
                    // 这是预期的错误
                    resultDiv.className = 'test-result result-success';
                    resultDiv.innerHTML = `
                        <h4>✅ API失败模拟成功</h4>
                        <p><strong>模拟的错误:</strong> ${apiError.message}</p>
                        <p><strong>错误处理:</strong> 系统正确捕获并处理了API错误</p>
                        <p><strong>降级机制:</strong> 在实际使用中，系统会自动切换到静态数据</p>
                        <p><em>这证明了错误处理机制工作正常</em></p>
                    `;
                }

            } catch (error) {
                resultDiv.className = 'test-result result-error';
                resultDiv.innerHTML = `
                    <h4>❌ API失败模拟测试失败</h4>
                    <p><strong>错误信息:</strong> ${error.message}</p>
                `;
            }
        }

        /**
         * @function compareAccountResults - 对比账号测试结果
         * @description 对比不同账号的测试结果
         */
        function compareAccountResults() {
            const resultDiv = document.getElementById('multiAccountResults');
            resultDiv.style.display = 'block';

            if (!testResults.multiAccount) {
                resultDiv.className = 'test-result result-warning';
                resultDiv.innerHTML = '📊 请先运行多账号测试以获取对比数据';
                return;
            }

            const results = testResults.multiAccount;
            const successAccounts = results.filter(r => r.success);
            const failedAccounts = results.filter(r => !r.success);

            let comparisonContent = `
                <h4>📊 账号测试结果对比</h4>
                <div class="grid-2">
                    <div>
                        <h5>✅ 成功账号 (${successAccounts.length})</h5>
                        <ul>
                            ${successAccounts.map(acc => `
                                <li><strong>${acc.account.name}:</strong> ${acc.account.email}</li>
                            `).join('')}
                        </ul>
                    </div>
                    <div>
                        <h5>❌ 失败账号 (${failedAccounts.length})</h5>
                        <ul>
                            ${failedAccounts.map(acc => `
                                <li><strong>${acc.account.name}:</strong> ${acc.error}</li>
                            `).join('')}
                        </ul>
                    </div>
                </div>
            `;

            if (successAccounts.length > 0) {
                comparisonContent += `
                    <h5>🔑 Token对比</h5>
                    <table class="comparison-table">
                        <thead>
                            <tr>
                                <th>账号</th>
                                <th>Token前缀</th>
                                <th>后台用户映射</th>
                            </tr>
                        </thead>
                        <tbody>
                            ${successAccounts.map(acc => `
                                <tr>
                                    <td>${acc.account.name}</td>
                                    <td>${acc.token.substring(0, 20)}...</td>
                                    <td>ID ${acc.account.backendUserId || '自动选择'}</td>
                                </tr>
                            `).join('')}
                        </tbody>
                    </table>
                `;
            }

            resultDiv.className = 'test-result result-success';
            resultDiv.innerHTML = comparisonContent;
        }

        /**
         * @function diagnoseAuthenticationIssues - 诊断认证问题
         * @description 专门诊断API认证问题，包括响应结构分析和兼容性检查
         */
        async function diagnoseAuthenticationIssues() {
            const resultDiv = document.getElementById('diagnosticResults');
            resultDiv.style.display = 'block';
            resultDiv.className = 'test-result result-pending';
            resultDiv.innerHTML = '🔄 正在诊断认证问题...';

            if (!currentAccount) {
                resultDiv.className = 'test-result result-error';
                resultDiv.innerHTML = '❌ 请先选择一个测试账号进行诊断';
                return;
            }

            try {
                const diagnosticResults = [];

                // 1. 基础连接测试
                diagnosticResults.push('🌐 <strong>基础连接测试</strong>');
                try {
                    const testUrl = `${API_CONFIG.BASE_URL}/health`;
                    const healthResponse = await fetch(testUrl, { method: 'GET' });
                    diagnosticResults.push(`✅ API服务器连接正常 (状态: ${healthResponse.status})`);
                } catch (error) {
                    diagnosticResults.push(`⚠️ API服务器连接测试失败: ${error.message}`);
                }

                // 2. 登录端点测试
                diagnosticResults.push('<br>🔐 <strong>登录端点测试</strong>');
                const loginUrl = `${API_CONFIG.BASE_URL}${API_CONFIG.ENDPOINTS.login}`;
                diagnosticResults.push(`📍 登录URL: ${loginUrl}`);

                const loginData = {
                    email: currentAccount.email,
                    password: currentAccount.password
                };
                diagnosticResults.push(`📤 登录数据: ${JSON.stringify(loginData, null, 2)}`);

                try {
                    // 执行原始fetch请求以获取详细信息
                    const response = await fetch(loginUrl, {
                        method: 'POST',
                        headers: {
                            'Content-Type': 'application/json',
                            'Accept': 'application/json'
                        },
                        body: JSON.stringify(loginData)
                    });

                    diagnosticResults.push(`📥 HTTP状态码: ${response.status} ${response.statusText}`);
                    diagnosticResults.push(`📋 响应头: ${JSON.stringify(Object.fromEntries(response.headers.entries()), null, 2)}`);

                    if (response.ok) {
                        const responseData = await response.json();
                        diagnosticResults.push(`📄 完整响应数据:`);
                        diagnosticResults.push(`<pre>${JSON.stringify(responseData, null, 2)}</pre>`);

                        // 3. Token字段分析
                        diagnosticResults.push('<br>🔍 <strong>Token字段分析</strong>');
                        const possibleTokenFields = [
                            'access_token', 'token', 'accessToken', 'authToken',
                            'bearer_token', 'jwt', 'api_token'
                        ];

                        let foundTokens = [];

                        // 检查顶级字段
                        possibleTokenFields.forEach(field => {
                            if (responseData[field]) {
                                foundTokens.push({
                                    path: field,
                                    value: responseData[field],
                                    type: typeof responseData[field]
                                });
                            }
                        });

                        // 检查嵌套字段
                        if (responseData.data) {
                            possibleTokenFields.forEach(field => {
                                if (responseData.data[field]) {
                                    foundTokens.push({
                                        path: `data.${field}`,
                                        value: responseData.data[field],
                                        type: typeof responseData.data[field]
                                    });
                                }
                            });
                        }

                        if (responseData.user) {
                            possibleTokenFields.forEach(field => {
                                if (responseData.user[field]) {
                                    foundTokens.push({
                                        path: `user.${field}`,
                                        value: responseData.user[field],
                                        type: typeof responseData.user[field]
                                    });
                                }
                            });
                        }

                        if (foundTokens.length > 0) {
                            diagnosticResults.push('✅ <strong>找到的Token字段:</strong>');
                            foundTokens.forEach(token => {
                                const preview = token.value.length > 50 ?
                                    token.value.substring(0, 50) + '...' : token.value;
                                diagnosticResults.push(`&nbsp;&nbsp;• ${token.path}: ${preview} (类型: ${token.type})`);
                            });

                            // 提供修复建议
                            diagnosticResults.push('<br>💡 <strong>修复建议:</strong>');
                            diagnosticResults.push(`建议使用字段: <code>${foundTokens[0].path}</code>`);
                            diagnosticResults.push(`修复代码: <code>response.${foundTokens[0].path}</code>`);
                        } else {
                            diagnosticResults.push('❌ <strong>未找到任何Token字段</strong>');
                            diagnosticResults.push('🔍 <strong>可用字段:</strong>');
                            Object.keys(responseData).forEach(key => {
                                diagnosticResults.push(`&nbsp;&nbsp;• ${key}: ${typeof responseData[key]}`);
                            });
                        }

                        // 4. 响应结构建议
                        diagnosticResults.push('<br>📋 <strong>响应结构建议</strong>');
                        if (responseData.success !== undefined) {
                            diagnosticResults.push(`✅ 包含success字段: ${responseData.success}`);
                        }
                        if (responseData.message) {
                            diagnosticResults.push(`📝 包含message字段: ${responseData.message}`);
                        }
                        if (responseData.data) {
                            diagnosticResults.push(`📦 包含data字段 (类型: ${typeof responseData.data})`);
                        }

                    } else {
                        const errorText = await response.text();
                        diagnosticResults.push(`❌ 登录请求失败`);
                        diagnosticResults.push(`📄 错误响应: ${errorText}`);

                        // 常见错误分析
                        if (response.status === 401) {
                            diagnosticResults.push('🔍 <strong>401错误分析:</strong> 账号密码可能不正确');
                        } else if (response.status === 404) {
                            diagnosticResults.push('🔍 <strong>404错误分析:</strong> 登录端点可能不存在或URL错误');
                        } else if (response.status === 500) {
                            diagnosticResults.push('🔍 <strong>500错误分析:</strong> 服务器内部错误，可能是请求格式问题');
                        }
                    }

                } catch (fetchError) {
                    diagnosticResults.push(`❌ 网络请求失败: ${fetchError.message}`);

                    // 网络错误分析
                    if (fetchError.name === 'TypeError') {
                        diagnosticResults.push('🔍 <strong>网络错误分析:</strong> 可能是CORS问题或网络连接问题');
                    }
                }

                // 5. 环境检查
                diagnosticResults.push('<br>🌍 <strong>环境检查</strong>');
                diagnosticResults.push(`🌐 当前域名: ${window.location.origin}`);
                diagnosticResults.push(`📍 API基础URL: ${API_CONFIG.BASE_URL}`);
                diagnosticResults.push(`🔗 完整登录URL: ${loginUrl}`);
                diagnosticResults.push(`👤 测试账号: ${currentAccount.email}`);

                // 6. 建议的解决方案
                diagnosticResults.push('<br>💡 <strong>建议的解决方案</strong>');
                diagnosticResults.push('1. 检查API响应中的实际token字段名称');
                diagnosticResults.push('2. 确认账号密码是否正确');
                diagnosticResults.push('3. 验证API端点URL是否正确');
                diagnosticResults.push('4. 检查网络连接和CORS设置');
                diagnosticResults.push('5. 查看浏览器开发者工具的Network标签页');

                resultDiv.className = 'test-result result-warning';
                resultDiv.innerHTML = `
                    <h4>🔐 认证问题诊断报告</h4>
                    <div style="text-align: left; line-height: 1.6;">
                        ${diagnosticResults.join('<br>')}
                    </div>
                `;

            } catch (error) {
                resultDiv.className = 'test-result result-error';
                resultDiv.innerHTML = `
                    <h4>❌ 认证诊断失败</h4>
                    <p><strong>错误信息:</strong> ${error.message}</p>
                    <p><strong>建议:</strong> 请检查网络连接和API配置</p>
                `;
            }
        }

        /**
         * @function testAlternativeLoginEndpoints - 测试备用登录端点
         * @description 测试不同的登录端点路径，寻找正确的API端点
         */
        async function testAlternativeLoginEndpoints() {
            const resultDiv = document.getElementById('coreTestResults');
            resultDiv.style.display = 'block';
            resultDiv.className = 'test-result result-pending';
            resultDiv.innerHTML = '🔄 正在测试备用登录端点...';

            if (!currentAccount) {
                resultDiv.className = 'test-result result-error';
                resultDiv.innerHTML = '❌ 请先选择一个测试账号';
                return;
            }

            const alternativeEndpoints = [
                '/login',
                '/auth/login',
                '/api/login',
                '/api/auth/login',
                '/user/login',
                '/admin/login',
                '/v1/login',
                '/v1/auth/login'
            ];

            const loginData = {
                email: currentAccount.email,
                password: currentAccount.password
            };

            const testResults = [];

            for (const endpoint of alternativeEndpoints) {
                try {
                    const url = `${API_CONFIG.BASE_URL}${endpoint}`;
                    console.log(`🧪 测试端点: ${url}`);

                    const response = await fetch(url, {
                        method: 'POST',
                        headers: {
                            'Content-Type': 'application/json',
                            'Accept': 'application/json'
                        },
                        body: JSON.stringify(loginData)
                    });

                    const responseData = await response.json();

                    testResults.push({
                        endpoint: endpoint,
                        status: response.status,
                        success: response.ok,
                        hasToken: !!(responseData.access_token || responseData.token || responseData.authToken),
                        responseKeys: Object.keys(responseData || {}),
                        data: responseData
                    });

                } catch (error) {
                    testResults.push({
                        endpoint: endpoint,
                        status: 'ERROR',
                        success: false,
                        hasToken: false,
                        error: error.message,
                        data: null
                    });
                }
            }

            // 分析结果
            const successfulEndpoints = testResults.filter(r => r.success);
            const tokenEndpoints = testResults.filter(r => r.hasToken);

            let resultHTML = `
                <h4>🔄 备用登录端点测试结果</h4>
                <p><strong>测试账号:</strong> ${currentAccount.email}</p>
                <p><strong>成功端点:</strong> ${successfulEndpoints.length}/${testResults.length}</p>
                <p><strong>包含Token的端点:</strong> ${tokenEndpoints.length}/${testResults.length}</p>

                <table class="comparison-table">
                    <thead>
                        <tr>
                            <th>端点</th>
                            <th>状态</th>
                            <th>包含Token</th>
                            <th>响应字段</th>
                        </tr>
                    </thead>
                    <tbody>
            `;

            testResults.forEach(result => {
                const statusIcon = result.success ? '✅' : '❌';
                const tokenIcon = result.hasToken ? '🔑' : '❌';
                const statusText = result.status === 'ERROR' ? result.error : result.status;

                resultHTML += `
                    <tr>
                        <td>${result.endpoint}</td>
                        <td>${statusIcon} ${statusText}</td>
                        <td>${tokenIcon}</td>
                        <td>${result.responseKeys ? result.responseKeys.join(', ') : 'N/A'}</td>
                    </tr>
                `;
            });

            resultHTML += `
                    </tbody>
                </table>
            `;

            if (tokenEndpoints.length > 0) {
                resultHTML += `
                    <h5>🔑 推荐使用的端点:</h5>
                    <ul>
                `;
                tokenEndpoints.forEach(endpoint => {
                    resultHTML += `<li><strong>${endpoint.endpoint}</strong> - 状态: ${endpoint.status}</li>`;
                });
                resultHTML += `</ul>`;
            }

            resultDiv.className = successfulEndpoints.length > 0 ? 'test-result result-success' : 'test-result result-warning';
            resultDiv.innerHTML = resultHTML;
        }

        /**
         * @function quickAuthFix - 快速认证修复
         * @description 尝试自动修复认证问题，包括端点切换和响应格式适配
         */
        async function quickAuthFix() {
            const resultDiv = document.getElementById('coreTestResults');
            resultDiv.style.display = 'block';
            resultDiv.className = 'test-result result-pending';
            resultDiv.innerHTML = '🔄 正在执行快速认证修复...';

            if (!currentAccount) {
                resultDiv.className = 'test-result result-error';
                resultDiv.innerHTML = '❌ 请先选择一个测试账号';
                return;
            }

            const fixSteps = [];
            let fixSuccess = false;

            try {
                // 步骤1: 测试当前端点
                fixSteps.push('🔍 步骤1: 测试当前登录端点');
                try {
                    const result = await authenticateAccount(currentAccount.id);
                    fixSteps.push('✅ 当前端点工作正常，认证成功');
                    fixSuccess = true;
                } catch (error) {
                    fixSteps.push(`❌ 当前端点失败: ${error.message}`);

                    // 步骤2: 尝试备用端点
                    fixSteps.push('🔄 步骤2: 尝试备用登录端点');
                    const alternativeEndpoints = ['/auth/login', '/api/auth/login', '/user/login'];

                    for (const endpoint of alternativeEndpoints) {
                        try {
                            // 临时修改端点
                            const originalEndpoint = API_CONFIG.ENDPOINTS.login;
                            API_CONFIG.ENDPOINTS.login = endpoint;

                            const result = await authenticateAccount(currentAccount.id);
                            fixSteps.push(`✅ 备用端点 ${endpoint} 成功`);
                            fixSuccess = true;
                            break;

                        } catch (endpointError) {
                            fixSteps.push(`❌ 备用端点 ${endpoint} 失败`);
                            // 恢复原始端点
                            API_CONFIG.ENDPOINTS.login = originalEndpoint;
                        }
                    }
                }

                // 步骤3: 如果仍然失败，尝试不同的请求格式
                if (!fixSuccess) {
                    fixSteps.push('🔄 步骤3: 尝试不同的请求格式');

                    const alternativeFormats = [
                        { username: currentAccount.email, password: currentAccount.password },
                        { user: currentAccount.email, pass: currentAccount.password },
                        { login: currentAccount.email, password: currentAccount.password }
                    ];

                    for (const format of alternativeFormats) {
                        try {
                            const response = await makeAPICall(API_CONFIG.ENDPOINTS.login, format, 'POST', currentAccount.id);

                            // 检查响应中的token
                            const possibleTokenFields = ['access_token', 'token', 'authToken', 'bearer_token'];
                            let foundToken = null;

                            for (const field of possibleTokenFields) {
                                if (response[field]) {
                                    foundToken = response[field];
                                    break;
                                }
                            }

                            if (foundToken) {
                                authTokens[currentAccount.id] = foundToken;
                                fixSteps.push(`✅ 替代格式成功: ${JSON.stringify(format)}`);
                                fixSuccess = true;
                                break;
                            }

                        } catch (formatError) {
                            fixSteps.push(`❌ 替代格式失败: ${JSON.stringify(format)}`);
                        }
                    }
                }

                // 步骤4: 网络和环境检查
                if (!fixSuccess) {
                    fixSteps.push('🔄 步骤4: 网络和环境检查');

                    try {
                        const healthCheck = await fetch(`${API_CONFIG.BASE_URL}/health`, { method: 'GET' });
                        fixSteps.push(`✅ 服务器连接正常 (状态: ${healthCheck.status})`);
                    } catch (healthError) {
                        fixSteps.push(`❌ 服务器连接失败: ${healthError.message}`);
                    }

                    fixSteps.push(`📍 当前API基础URL: ${API_CONFIG.BASE_URL}`);
                    fixSteps.push(`🌐 当前域名: ${window.location.origin}`);
                }

                // 生成修复报告
                let resultHTML = `
                    <h4>${fixSuccess ? '✅' : '❌'} 快速认证修复${fixSuccess ? '成功' : '失败'}</h4>
                    <p><strong>测试账号:</strong> ${currentAccount.email}</p>
                    <div style="text-align: left;">
                        ${fixSteps.map(step => `<p>${step}</p>`).join('')}
                    </div>
                `;

                if (fixSuccess) {
                    resultHTML += `
                        <p><strong>✅ 修复成功！</strong> 账号已成功认证。</p>
                        <p><strong>Token:</strong> ${authTokens[currentAccount.id].substring(0, 20)}...</p>
                    `;
                } else {
                    resultHTML += `
                        <p><strong>❌ 自动修复失败</strong></p>
                        <p><strong>建议:</strong></p>
                        <ul style="text-align: left;">
                            <li>点击"🔐 认证问题诊断"获取详细分析</li>
                            <li>检查账号密码是否正确</li>
                            <li>确认API服务器是否正常运行</li>
                            <li>查看浏览器开发者工具的Network标签页</li>
                        </ul>
                    `;
                }

                resultDiv.className = fixSuccess ? 'test-result result-success' : 'test-result result-warning';
                resultDiv.innerHTML = resultHTML;

            } catch (error) {
                resultDiv.className = 'test-result result-error';
                resultDiv.innerHTML = `
                    <h4>❌ 快速修复过程失败</h4>
                    <p><strong>错误信息:</strong> ${error.message}</p>
                    <div style="text-align: left;">
                        ${fixSteps.map(step => `<p>${step}</p>`).join('')}
                    </div>
                `;
            }
        }

        // ========== 随机数据生成函数 ==========

        /**
         * @function generateRandomTestData - 生成随机测试数据
         * @description 为手动测试表单生成随机的订单数据
         */
        function generateRandomTestData() {
            // 随机地址数据
            const pickupLocations = [
                'KLIA Airport', 'KLIA2 Airport', 'Subang Airport', 'KL Sentral',
                'Pavilion KL', 'KLCC', 'Bukit Bintang', 'Mid Valley Megamall',
                'Sunway Pyramid', 'One Utama', 'The Gardens Mall', 'IOI City Mall'
            ];

            const destinations = [
                'KLCC', 'Petronas Twin Towers', 'Batu Caves', 'Genting Highlands',
                'Putrajaya', 'Malacca', 'Port Dickson', 'Sunway Lagoon',
                'KL Tower', 'Merdeka Square', 'Central Market', 'Chinatown KL'
            ];

            // 随机客户数据
            const customerNames = [
                '张三', '李四', '王五', '赵六', '陈七', '刘八',
                'John Smith', 'Mary Johnson', 'David Brown', 'Sarah Wilson',
                'Ahmad Rahman', 'Siti Nurhaliza', 'Raj Kumar', 'Priya Sharma'
            ];

            const phoneNumbers = [
                '+60123456789', '+60187654321', '+60198765432', '+60176543210',
                '+60134567890', '+60145678901', '+60156789012', '+60167890123'
            ];

            // 生成随机日期（未来30天内）
            const today = new Date();
            const futureDate = new Date(today.getTime() + Math.random() * 30 * 24 * 60 * 60 * 1000);
            const formattedDate = `${String(futureDate.getDate()).padStart(2, '0')}-${String(futureDate.getMonth() + 1).padStart(2, '0')}-${futureDate.getFullYear()}`;

            // 生成随机时间
            const hours = Math.floor(Math.random() * 24);
            const minutes = Math.floor(Math.random() * 60);
            const formattedTime = `${String(hours).padStart(2, '0')}:${String(minutes).padStart(2, '0')}`;

            // 随机订单类型
            const orderTypes = ['pickup', 'dropoff', 'charter'];
            const randomOrderType = orderTypes[Math.floor(Math.random() * orderTypes.length)];

            // 随机乘客人数
            const passengerCount = Math.floor(Math.random() * 8) + 1;

            // 更新表单字段
            document.getElementById('testOrderType').value = randomOrderType;
            document.getElementById('testPassengerCount').value = passengerCount;
            document.getElementById('testPickup').value = pickupLocations[Math.floor(Math.random() * pickupLocations.length)];
            document.getElementById('testDestination').value = destinations[Math.floor(Math.random() * destinations.length)];
            document.getElementById('testCustomerName').value = customerNames[Math.floor(Math.random() * customerNames.length)];
            document.getElementById('testCustomerPhone').value = phoneNumbers[Math.floor(Math.random() * phoneNumbers.length)];
            document.getElementById('testCustomerEmail').value = `test${Math.floor(Math.random() * 1000)}@example.com`;
            document.getElementById('testPickupDate').value = formattedDate;
            document.getElementById('testPickupTime').value = formattedTime;

            // 显示生成结果
            const resultDiv = document.getElementById('manualTestResults');
            resultDiv.style.display = 'block';
            resultDiv.className = 'test-result result-success';
            resultDiv.innerHTML = `
                <h4>🎲 随机测试数据已生成</h4>
                <p><strong>订单类型:</strong> ${randomOrderType}</p>
                <p><strong>乘客人数:</strong> ${passengerCount}</p>
                <p><strong>接机地点:</strong> ${document.getElementById('testPickup').value}</p>
                <p><strong>目的地:</strong> ${document.getElementById('testDestination').value}</p>
                <p><strong>客户姓名:</strong> ${document.getElementById('testCustomerName').value}</p>
                <p><strong>日期时间:</strong> ${formattedDate} ${formattedTime}</p>
                <p><em>数据已填入表单，可直接创建订单或继续编辑</em></p>
            `;
        }

        /**
         * @function generateRandomOrderData - 生成随机订单数据
         * @param {string} orderType - 订单类型，如果为'mixed'则随机选择
         * @returns {Object} 随机生成的订单数据
         * @description 为批量测试生成随机订单数据
         */
        function generateRandomOrderData(orderType = 'mixed') {
            // 随机地址数据
            const pickupLocations = [
                'KLIA Airport', 'KLIA2 Airport', 'Subang Airport', 'KL Sentral',
                'Pavilion KL', 'KLCC', 'Bukit Bintang', 'Mid Valley Megamall',
                'Sunway Pyramid', 'One Utama', 'The Gardens Mall', 'IOI City Mall',
                'Genting Highlands', 'Putrajaya', 'Cyberjaya', 'Shah Alam'
            ];

            const destinations = [
                'KLCC', 'Petronas Twin Towers', 'Batu Caves', 'Genting Highlands',
                'Putrajaya', 'Malacca', 'Port Dickson', 'Sunway Lagoon',
                'KL Tower', 'Merdeka Square', 'Central Market', 'Chinatown KL',
                'Kuala Selangor', 'Fraser\'s Hill', 'Cameron Highlands', 'Ipoh'
            ];

            // 随机客户数据
            const customerNames = [
                '张三', '李四', '王五', '赵六', '陈七', '刘八', '周九', '吴十',
                'John Smith', 'Mary Johnson', 'David Brown', 'Sarah Wilson',
                'Michael Davis', 'Jennifer Garcia', 'Christopher Martinez', 'Lisa Anderson',
                'Ahmad Rahman', 'Siti Nurhaliza', 'Raj Kumar', 'Priya Sharma',
                'Tan Wei Ming', 'Lim Siew Ling', 'Wong Ah Kow', 'Lee Mei Hua'
            ];

            // 生成随机日期（未来30天内）
            const today = new Date();
            const futureDate = new Date(today.getTime() + Math.random() * 30 * 24 * 60 * 60 * 1000);
            const formattedDate = `${String(futureDate.getDate()).padStart(2, '0')}-${String(futureDate.getMonth() + 1).padStart(2, '0')}-${futureDate.getFullYear()}`;

            // 生成随机时间
            const hours = Math.floor(Math.random() * 24);
            const minutes = Math.floor(Math.random() * 60);
            const formattedTime = `${String(hours).padStart(2, '0')}:${String(minutes).padStart(2, '0')}`;

            // 确定订单类型
            let finalOrderType = orderType;
            if (orderType === 'mixed') {
                const orderTypes = ['pickup', 'dropoff', 'charter'];
                finalOrderType = orderTypes[Math.floor(Math.random() * orderTypes.length)];
            }

            // 随机乘客人数
            const passengerCount = Math.floor(Math.random() * 12) + 1;

            // 智能选择子分类
            const subCategory = apiData.subCategories.find(sc =>
                sc.name.toLowerCase().includes(finalOrderType) ||
                (sc.id === 2 && finalOrderType === 'pickup') ||
                (sc.id === 3 && finalOrderType === 'dropoff') ||
                (sc.id === 4 && finalOrderType === 'charter')
            ) || apiData.subCategories[0];

            // 智能选择车型（根据人数）
            const carType = apiData.carTypes.find(ct => {
                const seatNumber = parseInt(ct.type.match(/(\d+)/)?.[1] || '0');
                return seatNumber >= passengerCount;
            }) || apiData.carTypes.find(ct => ct.id === 5); // 默认5座

            // 获取后台用户ID
            const backendUserId = currentAccount?.backendUserId ||
                                (apiData.backendUsers[0] ? apiData.backendUsers[0].id : 1);

            // 随机选择语言
            const availableLanguages = apiData.languages.filter(lang =>
                [2, 3, 4].includes(lang.id) // English, Malay, Chinese
            );
            const selectedLanguages = availableLanguages.slice(0, Math.floor(Math.random() * 3) + 1);
            const languagesArray = {};
            selectedLanguages.forEach((lang, index) => {
                languagesArray[index.toString()] = lang.id.toString();
            });

            return {
                pickup_location: pickupLocations[Math.floor(Math.random() * pickupLocations.length)],
                destination: destinations[Math.floor(Math.random() * destinations.length)],
                pickup_date: formattedDate,
                pickup_time: formattedTime,
                passenger_count: passengerCount,
                car_type_id: carType?.id || 5,
                sub_category_id: subCategory?.id || 2,
                languages_id_array: languagesArray,
                incharge_by_backend_user_id: backendUserId,
                customer_name: customerNames[Math.floor(Math.random() * customerNames.length)],
                customer_phone: `+601${Math.floor(Math.random() * 90000000) + 10000000}`,
                customer_email: `test${Math.floor(Math.random() * 10000)}@example.com`,
                remarks: `批量测试订单 #${Math.floor(Math.random() * 10000)} - ${new Date().toLocaleString()}`,
                // 额外的测试字段
                flight_number: Math.random() > 0.5 ? `MH${Math.floor(Math.random() * 9000) + 1000}` : '',
                special_requirements: Math.random() > 0.7 ? '需要儿童座椅' : ''
            };
        }

        // ========== 批量测试函数 ==========

        /**
         * @function startBatchOrderTest - 开始批量订单测试
         * @description 启动批量订单创建测试，支持进度显示和统计
         */
        async function startBatchOrderTest() {
            if (!currentAccount) {
                alert('请先选择一个测试账号');
                return;
            }

            // 确保账号已认证
            if (!authTokens[currentAccount.id]) {
                try {
                    await authenticateAccount(currentAccount.id);
                } catch (error) {
                    alert(`认证失败: ${error.message}`);
                    return;
                }
            }

            // 获取配置参数
            const orderCount = parseInt(document.getElementById('batchOrderCount').value);
            const orderDelay = parseInt(document.getElementById('batchOrderDelay').value);
            const orderType = document.getElementById('batchOrderType').value;

            if (orderCount < 1 || orderCount > 50) {
                alert('订单数量必须在1-50之间');
                return;
            }

            // 初始化批量测试状态
            batchTestState = {
                isRunning: true,
                currentIndex: 0,
                totalCount: orderCount,
                successCount: 0,
                failCount: 0,
                orders: [],
                timeoutId: null
            };

            // 显示进度界面
            const progressDiv = document.getElementById('batchProgress');
            progressDiv.style.display = 'block';

            const resultDiv = document.getElementById('batchTestResults');
            resultDiv.style.display = 'block';
            resultDiv.className = 'test-result result-pending';
            resultDiv.innerHTML = `
                <h4>🚀 批量订单测试进行中...</h4>
                <p><strong>测试配置:</strong></p>
                <ul>
                    <li>订单数量: ${orderCount}</li>
                    <li>订单间隔: ${orderDelay}ms</li>
                    <li>订单类型: ${orderType === 'mixed' ? '混合类型' : orderType}</li>
                    <li>测试账号: ${currentAccount.email}</li>
                </ul>
                <div id="batchOrderDetails"></div>
            `;

            // 更新统计显示
            updateBatchProgress();

            // 开始创建订单
            await processBatchOrders(orderType, orderDelay);
        }

        /**
         * @function processBatchOrders - 处理批量订单创建
         * @param {string} orderType - 订单类型
         * @param {number} delay - 订单间隔时间
         * @description 逐个创建批量订单，支持中断和错误处理
         */
        async function processBatchOrders(orderType, delay) {
            while (batchTestState.isRunning && batchTestState.currentIndex < batchTestState.totalCount) {
                try {
                    // 生成随机订单数据
                    const orderData = generateRandomOrderData(orderType);

                    // 更新进度显示
                    document.getElementById('batchProgressText').textContent =
                        `正在创建订单 ${batchTestState.currentIndex + 1}/${batchTestState.totalCount}...`;

                    // 创建订单
                    const response = await makeAPICall(API_CONFIG.ENDPOINTS.create_order, orderData);

                    // 分析响应结构
                    const responseAnalysis = analyzeOrderResponse(response);

                    // 记录成功订单
                    batchTestState.orders.push({
                        index: batchTestState.currentIndex + 1,
                        status: 'success',
                        data: orderData,
                        response: response,
                        responseAnalysis: responseAnalysis,
                        error: null
                    });

                    batchTestState.successCount++;

                } catch (error) {
                    // 记录失败订单
                    batchTestState.orders.push({
                        index: batchTestState.currentIndex + 1,
                        status: 'failed',
                        data: generateRandomOrderData(orderType),
                        response: null,
                        error: error.message
                    });

                    batchTestState.failCount++;
                }

                batchTestState.currentIndex++;
                updateBatchProgress();

                // 如果不是最后一个订单，等待指定时间
                if (batchTestState.currentIndex < batchTestState.totalCount && batchTestState.isRunning) {
                    await new Promise(resolve => {
                        batchTestState.timeoutId = setTimeout(resolve, delay);
                    });
                }
            }

            // 测试完成
            if (batchTestState.isRunning) {
                completeBatchTest();
            }
        }

        /**
         * @function updateBatchProgress - 更新批量测试进度显示
         * @description 更新进度条、统计数据和状态文本
         */
        function updateBatchProgress() {
            const progress = (batchTestState.currentIndex / batchTestState.totalCount) * 100;

            // 更新进度条
            document.getElementById('batchProgressBar').style.width = `${progress}%`;

            // 更新统计数据
            document.getElementById('batchSuccessCount').textContent = batchTestState.successCount;
            document.getElementById('batchFailCount').textContent = batchTestState.failCount;
            document.getElementById('batchTotalCount').textContent = batchTestState.totalCount;

            // 更新进度文本
            if (batchTestState.currentIndex >= batchTestState.totalCount) {
                document.getElementById('batchProgressText').textContent = '测试完成';
            }
        }

        /**
         * @function completeBatchTest - 完成批量测试
         * @description 显示测试结果汇总和详细分析信息
         */
        async function completeBatchTest() {
            batchTestState.isRunning = false;

            const resultDiv = document.getElementById('batchTestResults');
            const successRate = ((batchTestState.successCount / batchTestState.totalCount) * 100).toFixed(1);

            // 生成详细测试报告
            let healthReport = null;
            try {
                healthReport = await validateEndpointHealth(currentAccount?.id);
            } catch (error) {
                console.warn('无法生成端点健康报告:', error);
            }

            const detailedReport = generateDetailedTestReport(batchTestState.orders, healthReport);

            // 分析订单ID问题
            const orderIdIssues = batchTestState.orders.filter(order =>
                order.status === 'success' && order.responseAnalysis && !order.responseAnalysis.orderId
            );

            const orderIdFound = batchTestState.orders.filter(order =>
                order.status === 'success' && order.responseAnalysis && order.responseAnalysis.orderId
            );

            // 生成订单详情表格
            const orderRows = batchTestState.orders.map(order => {
                let orderIdDisplay = '未返回';
                if (order.status === 'success' && order.responseAnalysis) {
                    if (order.responseAnalysis.orderId) {
                        orderIdDisplay = `${order.responseAnalysis.orderId} (${order.responseAnalysis.orderIdField})`;
                    } else if (order.responseAnalysis.possibleIds.length > 0) {
                        orderIdDisplay = `可能ID: ${order.responseAnalysis.possibleIds[0].value}`;
                    }
                } else if (order.status === 'failed') {
                    orderIdDisplay = order.error;
                }

                return `
                    <tr>
                        <td>#${order.index}</td>
                        <td>${order.status === 'success' ? '✅ 成功' : '❌ 失败'}</td>
                        <td>${order.data.pickup_location}</td>
                        <td>${order.data.destination}</td>
                        <td>${order.data.customer_name}</td>
                        <td>${orderIdDisplay}</td>
                    </tr>
                `;
            }).join('');

            // 生成响应分析报告
            let responseAnalysisHtml = '';
            if (orderIdIssues.length > 0) {
                responseAnalysisHtml = `
                    <div style="background: #fff3cd; border: 1px solid #ffeaa7; border-radius: 4px; padding: 15px; margin: 15px 0;">
                        <h5>⚠️ 订单ID分析报告</h5>
                        <p><strong>问题:</strong> ${orderIdIssues.length}个成功订单未返回标准订单ID</p>
                        <p><strong>可能原因:</strong></p>
                        <ul>
                            <li>API响应字段名称不是标准的'order_id'</li>
                            <li>订单ID可能在嵌套对象中（如data.id）</li>
                            <li>API版本差异导致响应格式变化</li>
                        </ul>
                        <details>
                            <summary>查看响应结构分析</summary>
                            <pre>${JSON.stringify(detailedReport.responseAnalysis, null, 2)}</pre>
                        </details>
                    </div>
                `;
            }

            // 生成端点健康报告
            let endpointHealthHtml = '';
            if (healthReport) {
                const healthColor = healthReport.overallHealth === 'healthy' ? '#28a745' :
                                  healthReport.overallHealth === 'warning' ? '#ffc107' : '#dc3545';

                endpointHealthHtml = `
                    <div style="background: #f8f9fa; border: 1px solid #dee2e6; border-radius: 4px; padding: 15px; margin: 15px 0;">
                        <h5>🔍 API端点健康状态</h5>
                        <p><strong>整体状态:</strong> <span style="color: ${healthColor}; font-weight: bold;">${healthReport.overallHealth.toUpperCase()}</span></p>
                        <div class="grid-2">
                            <div>
                                <strong>正常端点 (${healthReport.workingEndpoints.length}):</strong>
                                <ul>
                                    ${healthReport.workingEndpoints.map(ep =>
                                        `<li>${ep.name} (${ep.responseTime}ms)</li>`
                                    ).join('')}
                                </ul>
                            </div>
                            <div>
                                <strong>失败端点 (${healthReport.failingEndpoints.length}):</strong>
                                <ul>
                                    ${healthReport.failingEndpoints.map(ep =>
                                        `<li style="color: #dc3545;">${ep.name}: ${ep.error}</li>`
                                    ).join('')}
                                </ul>
                            </div>
                        </div>
                        ${healthReport.suggestions.length > 0 ? `
                            <p><strong>建议:</strong></p>
                            <ul>
                                ${healthReport.suggestions.map(suggestion => `<li>${suggestion}</li>`).join('')}
                            </ul>
                        ` : ''}
                    </div>
                `;
            }

            resultDiv.className = `test-result ${batchTestState.successCount === batchTestState.totalCount ? 'result-success' : 'result-warning'}`;
            resultDiv.innerHTML = `
                <h4>🎉 批量订单测试完成</h4>
                <div class="grid-3" style="margin: 15px 0;">
                    <div style="text-align: center;">
                        <strong>总订单数:</strong><br>
                        <span style="font-size: 24px; color: #007bff;">${batchTestState.totalCount}</span>
                    </div>
                    <div style="text-align: center;">
                        <strong>成功率:</strong><br>
                        <span style="font-size: 24px; color: ${successRate >= 90 ? '#28a745' : successRate >= 70 ? '#ffc107' : '#dc3545'};">${successRate}%</span>
                    </div>
                    <div style="text-align: center;">
                        <strong>订单ID检测:</strong><br>
                        <span style="font-size: 18px; color: ${orderIdFound.length > 0 ? '#28a745' : '#dc3545'};">
                            ${orderIdFound.length}/${batchTestState.successCount} 找到
                        </span>
                    </div>
                </div>

                ${responseAnalysisHtml}
                ${endpointHealthHtml}

                <h5>📋 订单详情</h5>
                <table class="comparison-table">
                    <thead>
                        <tr>
                            <th>序号</th>
                            <th>状态</th>
                            <th>接机地点</th>
                            <th>目的地</th>
                            <th>客户姓名</th>
                            <th>订单ID/错误信息</th>
                        </tr>
                    </thead>
                    <tbody>
                        ${orderRows}
                    </tbody>
                </table>

                <div style="margin-top: 15px;">
                    <button type="button" class="btn btn-info" onclick="exportBatchResults()">💾 导出测试结果</button>
                    <button type="button" class="btn btn-warning" onclick="showDetailedAnalysis()">📊 详细分析报告</button>
                    <button type="button" class="btn btn-secondary" onclick="resetBatchTest()">🔄 重置测试</button>
                </div>
            `;
        }

        /**
         * @function stopBatchOrderTest - 停止批量订单测试
         * @description 中断正在进行的批量测试
         */
        function stopBatchOrderTest() {
            if (!batchTestState.isRunning) {
                alert('当前没有正在运行的批量测试');
                return;
            }

            batchTestState.isRunning = false;

            if (batchTestState.timeoutId) {
                clearTimeout(batchTestState.timeoutId);
                batchTestState.timeoutId = null;
            }

            document.getElementById('batchProgressText').textContent = '测试已停止';

            const resultDiv = document.getElementById('batchTestResults');
            resultDiv.className = 'test-result result-warning';
            resultDiv.innerHTML = `
                <h4>⏹️ 批量测试已停止</h4>
                <p><strong>已完成:</strong> ${batchTestState.currentIndex}/${batchTestState.totalCount} 个订单</p>
                <p><strong>成功:</strong> ${batchTestState.successCount} 个</p>
                <p><strong>失败:</strong> ${batchTestState.failCount} 个</p>
                <p><em>测试已被用户手动停止</em></p>
                <button type="button" class="btn btn-info" onclick="completeBatchTest()">📊 查看详细结果</button>
            `;
        }

        /**
         * @function exportBatchResults - 导出批量测试结果
         * @description 将批量测试结果导出为JSON文件
         */
        function exportBatchResults() {
            if (batchTestState.orders.length === 0) {
                alert('没有可导出的测试结果');
                return;
            }

            const exportData = {
                testInfo: {
                    timestamp: new Date().toISOString(),
                    account: currentAccount.email,
                    totalOrders: batchTestState.totalCount,
                    successCount: batchTestState.successCount,
                    failCount: batchTestState.failCount,
                    successRate: ((batchTestState.successCount / batchTestState.totalCount) * 100).toFixed(1) + '%'
                },
                orders: batchTestState.orders
            };

            const dataStr = JSON.stringify(exportData, null, 2);
            const dataBlob = new Blob([dataStr], { type: 'application/json' });
            const url = URL.createObjectURL(dataBlob);

            const link = document.createElement('a');
            link.href = url;
            link.download = `batch-test-results-${new Date().toISOString().split('T')[0]}.json`;
            link.click();

            URL.revokeObjectURL(url);

            alert('批量测试结果已导出');
        }

        /**
         * @function resetBatchTest - 重置批量测试
         * @description 清空批量测试状态和显示
         */
        function resetBatchTest() {
            batchTestState = {
                isRunning: false,
                currentIndex: 0,
                totalCount: 0,
                successCount: 0,
                failCount: 0,
                orders: [],
                timeoutId: null
            };

            document.getElementById('batchProgress').style.display = 'none';
            document.getElementById('batchTestResults').style.display = 'none';

            // 重置进度显示
            document.getElementById('batchProgressBar').style.width = '0%';
            document.getElementById('batchProgressText').textContent = '准备中...';
            document.getElementById('batchSuccessCount').textContent = '0';
            document.getElementById('batchFailCount').textContent = '0';
            document.getElementById('batchTotalCount').textContent = '0';
        }

        /**
         * @function testEndpointHealth - 测试端点健康状态
         * @description 检查所有API端点的可用性和响应状态
         */
        async function testEndpointHealth() {
            const resultDiv = document.getElementById('coreTestResults');
            resultDiv.style.display = 'block';
            resultDiv.className = 'test-result result-pending';
            resultDiv.innerHTML = '🔄 正在检查API端点健康状态...';

            if (!currentAccount) {
                resultDiv.className = 'test-result result-error';
                resultDiv.innerHTML = '❌ 请先选择一个测试账号';
                return;
            }

            try {
                // 确保账号已认证
                if (!authTokens[currentAccount.id]) {
                    await authenticateAccount(currentAccount.id);
                }

                const healthReport = await validateEndpointHealth(currentAccount.id);

                const healthColor = healthReport.overallHealth === 'healthy' ? '#28a745' :
                                  healthReport.overallHealth === 'warning' ? '#ffc107' : '#dc3545';

                const workingRows = healthReport.workingEndpoints.map(ep => `
                    <tr>
                        <td>✅ ${ep.name}</td>
                        <td>${ep.url}</td>
                        <td style="color: #28a745;">${ep.responseTime}ms</td>
                        <td>${ep.dataCount} 条记录</td>
                        <td>${ep.critical ? '关键' : '可选'}</td>
                    </tr>
                `).join('');

                const failingRows = healthReport.failingEndpoints.map(ep => `
                    <tr>
                        <td>❌ ${ep.name}</td>
                        <td>${ep.url}</td>
                        <td style="color: #dc3545;">失败</td>
                        <td>${ep.error}</td>
                        <td>${ep.critical ? '关键' : '可选'}</td>
                    </tr>
                `).join('');

                resultDiv.className = `test-result ${healthReport.overallHealth === 'healthy' ? 'result-success' : 'result-warning'}`;
                resultDiv.innerHTML = `
                    <h4>🏥 API端点健康检查报告</h4>
                    <div class="grid-3" style="margin: 15px 0;">
                        <div style="text-align: center;">
                            <strong>整体状态:</strong><br>
                            <span style="font-size: 20px; color: ${healthColor}; font-weight: bold;">
                                ${healthReport.overallHealth.toUpperCase()}
                            </span>
                        </div>
                        <div style="text-align: center;">
                            <strong>正常端点:</strong><br>
                            <span style="font-size: 20px; color: #28a745;">${healthReport.workingEndpoints.length}</span>
                        </div>
                        <div style="text-align: center;">
                            <strong>失败端点:</strong><br>
                            <span style="font-size: 20px; color: #dc3545;">${healthReport.failingEndpoints.length}</span>
                        </div>
                    </div>

                    <h5>📊 端点详情</h5>
                    <table class="comparison-table">
                        <thead>
                            <tr>
                                <th>端点名称</th>
                                <th>URL</th>
                                <th>响应时间</th>
                                <th>数据/错误信息</th>
                                <th>重要性</th>
                            </tr>
                        </thead>
                        <tbody>
                            ${workingRows}
                            ${failingRows}
                        </tbody>
                    </table>

                    ${healthReport.criticalIssues.length > 0 ? `
                        <div style="background: #f8d7da; border: 1px solid #f5c6cb; border-radius: 4px; padding: 15px; margin: 15px 0;">
                            <h5>🚨 关键问题</h5>
                            <ul>
                                ${healthReport.criticalIssues.map(issue => `<li>${issue}</li>`).join('')}
                            </ul>
                        </div>
                    ` : ''}

                    ${healthReport.suggestions.length > 0 ? `
                        <div style="background: #d1ecf1; border: 1px solid #bee5eb; border-radius: 4px; padding: 15px; margin: 15px 0;">
                            <h5>💡 改进建议</h5>
                            <ul>
                                ${healthReport.suggestions.map(suggestion => `<li>${suggestion}</li>`).join('')}
                            </ul>
                        </div>
                    ` : ''}

                    <details>
                        <summary>查看完整健康报告</summary>
                        <pre>${JSON.stringify(healthReport, null, 2)}</pre>
                    </details>
                `;

            } catch (error) {
                resultDiv.className = 'test-result result-error';
                resultDiv.innerHTML = `
                    <h4>❌ 端点健康检查失败</h4>
                    <p><strong>错误信息:</strong> ${error.message}</p>
                    <p><strong>建议:</strong> 检查网络连接和账号认证状态</p>
                `;
            }
        }

        /**
         * @function testResponseStructure - 测试响应结构分析
         * @description 创建一个测试订单并分析响应结构
         */
        async function testResponseStructure() {
            const resultDiv = document.getElementById('coreTestResults');
            resultDiv.style.display = 'block';
            resultDiv.className = 'test-result result-pending';
            resultDiv.innerHTML = '🔄 正在分析API响应结构...';

            if (!currentAccount) {
                resultDiv.className = 'test-result result-error';
                resultDiv.innerHTML = '❌ 请先选择一个测试账号';
                return;
            }

            try {
                // 确保账号已认证
                if (!authTokens[currentAccount.id]) {
                    await authenticateAccount(currentAccount.id);
                }

                // 生成测试订单数据
                const testOrderData = generateRandomOrderData('pickup');

                // 创建测试订单
                const response = await makeAPICall(API_CONFIG.ENDPOINTS.create_order, testOrderData);

                // 分析响应结构
                const analysis = analyzeOrderResponse(response);

                resultDiv.className = 'test-result result-success';
                resultDiv.innerHTML = `
                    <h4>🔍 API响应结构分析报告</h4>

                    <div class="grid-2" style="margin: 15px 0;">
                        <div>
                            <h5>📋 基础信息</h5>
                            <ul>
                                <li><strong>响应类型:</strong> ${analysis.responseStructure.responseType}</li>
                                <li><strong>顶级字段数:</strong> ${analysis.responseStructure.topLevelKeys.length}</li>
                                <li><strong>包含data对象:</strong> ${analysis.responseStructure.hasData ? '是' : '否'}</li>
                                <li><strong>包含嵌套对象:</strong> ${analysis.responseStructure.hasNestedObjects ? '是' : '否'}</li>
                            </ul>
                        </div>
                        <div>
                            <h5>🔑 订单ID检测</h5>
                            <ul>
                                <li><strong>找到订单ID:</strong> ${analysis.orderId ? '是' : '否'}</li>
                                <li><strong>订单ID值:</strong> ${analysis.orderId || '未找到'}</li>
                                <li><strong>字段位置:</strong> ${analysis.orderIdField || '无'}</li>
                                <li><strong>可能ID数量:</strong> ${analysis.possibleIds.length}</li>
                            </ul>
                        </div>
                    </div>

                    <h5>📊 响应字段结构</h5>
                    <p><strong>顶级字段:</strong> ${analysis.responseStructure.topLevelKeys.join(', ')}</p>
                    ${analysis.responseStructure.hasData ? `
                        <p><strong>data字段内容:</strong> ${analysis.responseStructure.dataKeys?.join(', ') || '无'}</p>
                    ` : ''}

                    ${analysis.possibleIds.length > 0 ? `
                        <h5>🔍 发现的可能ID字段</h5>
                        <table class="comparison-table">
                            <thead>
                                <tr>
                                    <th>字段路径</th>
                                    <th>值</th>
                                    <th>位置</th>
                                    <th>类型</th>
                                </tr>
                            </thead>
                            <tbody>
                                ${analysis.possibleIds.map(id => `
                                    <tr>
                                        <td><code>${id.field}</code></td>
                                        <td>${id.value}</td>
                                        <td>${id.location}</td>
                                        <td>${id.type}</td>
                                    </tr>
                                `).join('')}
                            </tbody>
                        </table>
                    ` : ''}

                    ${analysis.warnings.length > 0 ? `
                        <div style="background: #fff3cd; border: 1px solid #ffeaa7; border-radius: 4px; padding: 15px; margin: 15px 0;">
                            <h5>⚠️ 分析警告</h5>
                            <ul>
                                ${analysis.warnings.map(warning => `<li>${warning}</li>`).join('')}
                            </ul>
                        </div>
                    ` : ''}

                    <details>
                        <summary>查看完整API响应</summary>
                        <pre>${JSON.stringify(response, null, 2)}</pre>
                    </details>

                    <details>
                        <summary>查看完整分析结果</summary>
                        <pre>${JSON.stringify(analysis, null, 2)}</pre>
                    </details>
                `;

            } catch (error) {
                resultDiv.className = 'test-result result-error';
                resultDiv.innerHTML = `
                    <h4>❌ 响应结构分析失败</h4>
                    <p><strong>错误信息:</strong> ${error.message}</p>
                    <p><strong>建议:</strong> 检查订单创建API是否正常工作</p>
                `;
            }
        }

        /**
         * @function showDetailedAnalysis - 显示详细分析报告
         * @description 显示批量测试的详细分析报告
         */
        function showDetailedAnalysis() {
            if (batchTestState.orders.length === 0) {
                alert('没有可分析的测试数据');
                return;
            }

            const detailedReport = generateDetailedTestReport(batchTestState.orders);

            const resultDiv = document.getElementById('batchTestResults');
            resultDiv.innerHTML += `
                <div style="background: #f8f9fa; border: 1px solid #dee2e6; border-radius: 4px; padding: 20px; margin: 20px 0;">
                    <h4>📊 详细分析报告</h4>

                    <h5>📈 测试统计</h5>
                    <ul>
                        <li><strong>总订单数:</strong> ${detailedReport.summary.totalOrders}</li>
                        <li><strong>成功数:</strong> ${detailedReport.summary.successCount}</li>
                        <li><strong>失败数:</strong> ${detailedReport.summary.failCount}</li>
                        <li><strong>成功率:</strong> ${detailedReport.summary.successRate}%</li>
                    </ul>

                    <h5>🔍 响应分析</h5>
                    <p><strong>订单ID问题数:</strong> ${detailedReport.responseAnalysis.orderIdIssues.length}</p>
                    <p><strong>响应结构数:</strong> ${detailedReport.responseAnalysis.responseStructures.length}</p>

                    ${detailedReport.improvements.length > 0 ? `
                        <h5>💡 改进建议</h5>
                        <table class="comparison-table">
                            <thead>
                                <tr>
                                    <th>优先级</th>
                                    <th>类别</th>
                                    <th>问题</th>
                                    <th>建议</th>
                                </tr>
                            </thead>
                            <tbody>
                                ${detailedReport.improvements.map(imp => `
                                    <tr>
                                        <td><span style="color: ${imp.priority === 'high' ? '#dc3545' : imp.priority === 'medium' ? '#ffc107' : '#28a745'};">
                                            ${imp.priority.toUpperCase()}
                                        </span></td>
                                        <td>${imp.category}</td>
                                        <td>${imp.issue}</td>
                                        <td>${imp.recommendation}</td>
                                    </tr>
                                `).join('')}
                            </tbody>
                        </table>
                    ` : ''}

                    <details>
                        <summary>查看完整分析数据</summary>
                        <pre>${JSON.stringify(detailedReport, null, 2)}</pre>
                    </details>
                </div>
            `;
        }

        // ========== 页面初始化 ==========

        // 页面加载完成后初始化应用
        document.addEventListener('DOMContentLoaded', initializeApp);

    </script>
</body>
</html>